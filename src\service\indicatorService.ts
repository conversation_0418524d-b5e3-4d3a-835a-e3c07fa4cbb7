import http from '@/axios'
import type { getCompareIndicatorDataParams, getIndicatorResultParams, getIndicatorResultResponse, getCompareIndicatorDataResponse } from '@/types/indicator'

export default {
    getCompareIndicatorData(body: getCompareIndicatorDataParams) {
        return http.get(`/api/zhenqi-report/indicator/get-compare-indicator-data`, {
            params: body
        })
    },
    getIndicatorResult(data: getIndicatorResultParams): Promise<getIndicatorResultResponse> {
        return http.post(`/api/indicator/data/getIndicatorResult`, data)
    },

    getDecomposeCompareIndicatorData(body: getCompareIndicatorDataParams): Promise<getCompareIndicatorDataResponse> {
        return http.get(`/api/zhenqi-report/indicator/get-decompose-compare-indicator-data`, {
            params: body
        })
    },
    getCreditLevel(body: {
        socialCreditCode: string
    }) {
        return http.get(`/api/zhenqi-report/indicator/get-credit-level`, {
            params: body
        })
    },

}