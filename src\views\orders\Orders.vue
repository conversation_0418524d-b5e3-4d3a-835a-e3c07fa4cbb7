<script lang="ts" setup>
import { ref } from 'vue'
import { OrderList } from './components'

const active = ref(0)
</script>

<template>
    <div class="orders back-color-main">
        <van-tabs v-model:active="active">
            <van-tab title="全部">
                <OrderList type="" />
            </van-tab>
            <van-tab title="待付款">
                <OrderList type="0" />
            </van-tab>
            <van-tab title="已付款">
                <OrderList type="1" />
            </van-tab>
            <van-tab title="已取消">
                <OrderList type="4" />
            </van-tab>
            <van-tab title="已关闭">
                <OrderList type="2" />
            </van-tab>
        </van-tabs>
    </div>
</template>

<style lang="scss" scoped>
.orders {
    height: 100%;
    display: flex;
    flex-direction: column;
    overflow: auto;
}

// .orders .van-tabs {
//     // height: 100%;
//     display: flex;
//     flex-direction: column;
//     flex: 1;
// }

// .orders :deep(.van-tabs__content) {
//     flex: 1;
// }

// .orders :deep(.van-tab__panel) {
//     height: 100%;
// }
</style>
