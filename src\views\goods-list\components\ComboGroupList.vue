<script lang="ts" setup>
import Icon from '@/components/common/Icon.vue'
import orderService from '@/service/orderService'
import CdkInput from '@/views/goods-list/components/CdkInput.vue'
import type { IOrderServicePageItem, IOrderGoodsValidateCouponResult } from '@/types/order'
import { computed, onMounted, ref, watch } from 'vue'
const props = defineProps<{
    createOrder: ({ goodsId, couponCode }: { goodsId: string; quantity: number; couponCode?: string }) => void
}>()
const quantity = ref(1)
const active = ref(0)
const list = ref<IOrderServicePageItem[]>([])
const loading = ref(true)
const discountInfo = ref<IOrderGoodsValidateCouponResult | null>(null)

const formatPrice = (amount: number) => {
    return (Number(amount) / 100).toFixed(2)
}

const currentPrice = computed(() => {
    if (list.value.length === 0) return 0
    const { goods } = list.value[active.value]
    const { goods_amount, goods_discount } = goods
    const unitPrice = goods_amount - goods_discount

    // return formatPrice(unitPrice * quantity.value)
    return unitPrice * quantity.value
})

const createOrder = () => {
    if (list.value.length === 0) return ''
    const currentItem = list.value[active.value]
    const { goods_id } = currentItem || {}
    if (discountInfo.value?.coupon_code) {
        props.createOrder({ goodsId: goods_id, quantity: quantity.value, couponCode: discountInfo.value?.coupon_code })
    } else {
        props.createOrder({ goodsId: goods_id, quantity: quantity.value })
    }
}

const quantityPlus = () => {
    console.log(typeof quantity.value)
    quantity.value = Number(quantity.value) + 1
}

const quantityReduce = () => {
    if (Number(quantity.value) === 1) return
    quantity.value = Number(quantity.value) - 1
}

const getList = () => {
    loading.value = true
    orderService
        .goodsServiceGoodsPage({
            page: 1,
            pageSize: 99,
            serviceKey: 'applet.checkup',
        })
        .then((res) => {
            loading.value = false
            if (res.errCode === 0) {
                list.value = res.data
            }
        })
        .catch(() => {
            loading.value = false
        })
}

const formatUnitDisplay = (amount: number, unit: string, quantity: string) => {
    try {
        const lastAmount = (amount / 100).toFixed(2)
        const lastUnit = unit
        const lastQuantity = Number(quantity).toFixed(0)

        return `${lastAmount}/${lastUnit}x${lastQuantity}份`
    } catch (error) {
        console.log(error)
        return '-'
    }
}

watch(
    () => active.value,
    (newVal) => {
        console.log('newVal', newVal)
        discountInfo.value = null
    }
)
onMounted(() => {
    getList()
})

const updateDiscountInfo = (val?: IOrderGoodsValidateCouponResult) => {
    if (val) {
        discountInfo.value = val
    } else {
        discountInfo.value = null
    }
}
</script>

<template>
    <template v-if="loading">
        <div class="tb-padding-12">
            <van-skeleton title :row="3" />
        </div>
    </template>
    <template v-if="!loading">
        <div class="flex flex-column height-100">
            <div class="goods-list-page flex flex-column gap-12 flex-wrap">
                <template v-for="(item, index) in list" :key="item.id">
                    <div
                        class="flex flex-column border-radius-8 tb-padding-10 lr-padding-16 card"
                        :class="{
                            'active-card': index === active,
                        }"
                        @click="active = index"
                    >
                        <div class="title font-16 lh-22 color-black b-margin-8">{{ item.goods.goods_name }}</div>
                        <div class="flex flex-column gap-8 b-margin-8">
                            <template v-for="service in item.service" :key="service.id">
                                <div class="flex flex-row space-between">
                                    <div class="flex flex-row gap-4">
                                        <div class="flex h-20 center">
                                            <Icon icon="icon-a-Frame1171275825" :size="18" color="var(--three-grey)" />
                                        </div>
                                        <div class="lh-20 font-14 color-three-grey">
                                            {{ service.service_item.service_name }}
                                        </div>
                                    </div>
                                    <div>
                                        <div class="flex flex-row baseline gap-2 color-three-grey">
                                            <div class="font-12 lh-16 flex">¥</div>
                                            <div class="font-16 lh-16 font-weight-500">
                                                {{ formatUnitDisplay(service.amount, service.unit, service.quantity) }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </template>
                        </div>
                        <div class="flex flex-row flex-row-end baseline gap-8">
                            <div class="flex flex-row flex-row-end">
                                <div class="flex flex-row baseline gap-2 color-three-grey">
                                    <div class="font-14 lh-20 flex color-three-grey">¥</div>
                                    <div class="font-20 lh-24 font-weight-500 color-three-grey line-through">
                                        {{ formatPrice(item.goods.goods_amount) }}
                                    </div>
                                </div>
                            </div>
                            <div class="flex flex-row flex-row-end">
                                <div class="flex flex-row baseline gap-2 color-black">
                                    <div class="font-20 lh-28 flex color-order">¥</div>
                                    <div class="font-28 lh-34 font-weight-500 color-order">
                                        {{ formatPrice(item.goods.goods_amount - item.goods.goods_discount) }}
                                    </div>
                                    <div class="font-20 lh-28 flex color-order">/年</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </template>
                <div>
                    <div class="flex flex-row top-bottom-center space-between">
                        <div class="flex flex-column gap-4">
                            <div class="lh-22 font-16">请选择需要购买的份数</div>
                            <div class="lh-16 font-12 color-three-grey">如有多家企业可购买多份</div>
                        </div>
                        <div class="flex flex-row gap-2">
                            <div class="w-24 h-24 reduce-btn" @click="quantityReduce">
                                <div class="font-16 lh-9">-</div>
                            </div>
                            <div class="w-40 h-24">
                                <van-field
                                    v-model="quantity"
                                    class="w-40 h-24 number-input"
                                    type="digit"
                                    :min="1"
                                    :max="99999"
                                />
                            </div>
                            <div class="w-24 h-24 plus-btn" @click="quantityPlus">
                                <div class="font-16 lh-9 b-margin-2">+</div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="flex flex-column gap-4">
                    <div class="lh-20 font-14">补充说明</div>
                    <div class="lh-16 font-12 color-three-grey">
                        1、该报告依赖企业的开票和纳税数据，需进行税局平台账号密码登录授权，或发给企业，输入税局账号密码进行授权
                    </div>
                    <div class="lh-16 font-12 color-three-grey">2、报告有效期为12个月，报告有效期内支持免费更新</div>
                    <div class="lh-16 font-12 color-three-grey">3、下单后暂不支持线上退款，请谨慎付款</div>
                </div>
            </div>
            <div class="flex flex-1 flex-row-end flex-column">
                <div class="display-flex flex-row-reverse b-margin-6">
                    <CdkInput
                        :original-amount="currentPrice"
                        :goods-ids="list[active].goods_id"
                        @updateDiscountInfo="updateDiscountInfo"
                        :code="discountInfo?.coupon_code"
                    />
                </div>
                <div class="small-primary-btn flex width-100 h-46 font-18" @click="createOrder">
                    立即支付 ¥{{
                        discountInfo?.coupon_id
                            ? formatPrice(currentPrice - discountInfo?.discount_amount)
                            : formatPrice(currentPrice || 0)
                    }}
                    <span
                        v-if="discountInfo?.original_amount"
                        class="l-margin-6 t-padding-2 font-14 font-weigth-500"
                        style="opacity: 0.7; text-decoration: line-through"
                    >
                        ￥{{ formatPrice(discountInfo?.original_amount) }}
                    </span>
                </div>
            </div>
        </div>
    </template>
</template>

<style scoped>
.active-card {
    border: 1px solid var(--order-color) !important;
    background-color: #ba76331a;
}

.active-card .title {
    color: var(--order-color);
}

.card {
    box-sizing: border-box;
    border: 1px solid var(--border-color);
}

.card .title {
    font-weight: 500;
}

.reduce-btn {
    border-top-left-radius: 4px;
    border-bottom-left-radius: 4px;
    background-color: #2626260f;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    line-height: 16px;
}

.plus-btn {
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    background-color: #2626260f;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 16px;
    line-height: 16px;
}

.number-input {
    padding: 0;
    height: 100%;
    display: flex;
    align-items: center;
    justify-content: center;
    background-color: #2626260f;
    font-size: 12px;
}

.number-input :deep(.van-field__control) {
    height: 100%;
    text-align: center;
}
</style>
