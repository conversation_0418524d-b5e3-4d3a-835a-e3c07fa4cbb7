<script lang="ts" setup>
import { computed, onMounted } from 'vue'
import { Brand, Search, Social, AiImg } from './components'
import HomeSwipe from './components/HomeSwipe.vue'
// import { tabbarheight } from '@/utils/tabbar-height'
import fileService from '@/service/fileService'

// const paddingBottom = computed(() => {
//     return tabbarheight() + 'px'
// })

import { useStore } from 'vuex'
import type { RootState } from '@/types/store'

const store = useStore<RootState>()

const oemInfo = computed(() => {
    const { oemConfig } = store.state.auth || {}
    const { modules } = oemConfig || {}
    const { config } = modules?.[0] || {}
    return config
})

const mpLogo = computed(() => {
    if(sessionStorage.getItem('oemConfig')){
        const oemConfig = JSON.parse(sessionStorage.getItem('oemConfig') as string)
        if(oemConfig && oemConfig.modules.length > 0 && oemConfig.modules[0].config.mpLogo ){
            return fileService.getFileUrl(oemConfig.modules[0].config.mpLogo)
        }
        return ''
    }else if (oemInfo.value?.mpLogo){
        return fileService.getFileUrl(oemInfo.value.mpLogo)
    }return ''
})

const mpHomeBanner = computed(() => {
    if(sessionStorage.getItem('oemConfig')){
        const oemConfig = JSON.parse(sessionStorage.getItem('oemConfig') as string)
        if(oemConfig && oemConfig.modules.length > 0 && oemConfig.modules[0].config.mpHomeBanner ){
            return fileService.getFileUrl(oemConfig.modules[0].config.mpHomeBanner)
        }
        return ''
    }else if (oemInfo.value?.mpHomeBanner){
        return fileService.getFileUrl(oemInfo.value.mpHomeBanner)
    }return ''
})

const mpHomeSpec = computed(() => {
    if(sessionStorage.getItem('oemConfig')){
        const oemConfig = JSON.parse(sessionStorage.getItem('oemConfig') as string)
        if(oemConfig && oemConfig.modules.length > 0 && oemConfig.modules[0].config.mpHomeSpec ){
            return fileService.getFileUrl(oemConfig.modules[0].config.mpHomeSpec)
        }
        return ''
    }else if (oemInfo.value?.mpHomeSpec){
        return fileService.getFileUrl(oemInfo.value.mpHomeSpec)
    }return ''
})

const pyr = computed(() => {
    if(sessionStorage.getItem('oemConfig')){
        const oemConfig = JSON.parse(sessionStorage.getItem('oemConfig') as string)
        if(oemConfig && oemConfig.modules.length > 0 && oemConfig.modules[0].config.pyr ){
            return oemConfig.modules[0].config.pyr
        }
        return false
    }else if (oemInfo.value?.pyr){
        return oemInfo.value.pyr
    }return false
})

onMounted(() => {

})

</script>

<template>
    <div class="home">
        <div class="wrap">
            <div v-if="mpLogo" class="home-top">
                <img :src="mpLogo" alt="main" class="height-100" />
            </div>
            <div v-if="mpHomeBanner" class="home-content">
                <img :src="mpHomeBanner" alt="main" class="width-100" />
            </div>
            <div v-else-if="pyr" class="home-content">
                <img src="https://zhenqi-cloud-bizs.oss-cn-shanghai.aliyuncs.com/temp/oem/20250801/aa20bfa0eb5594aadd8f2d26b96186bd_23e36779-2c9c-4a6b-82d0-7a9104be873b_1754040746115.png" alt="main" class="width-100" />
            </div>
            <div v-else >
                <Brand />
                <AiImg />
            </div>
            <Search />
            <div v-if="mpHomeSpec" class="home-spec">
                <img :src="mpHomeSpec" alt="main" class="height-100 width-100" />
            </div>
            <div v-else-if="pyr" class="home-spec">
                <img src="https://zhenqi-cloud-bizs.oss-cn-shanghai.aliyuncs.com/temp/oem/20250801/c9d82a889b5a84494f01f65654745529_cd706ab1-191e-4c21-b151-0c332bef908e_1754040780533.png" alt="main" class="width-100" />
            </div>
            <!-- <Desc /> -->
            <div v-else>
                <HomeSwipe />
                <Social />
            </div>

        </div>
    </div>
</template>

<style lang="scss" scoped>

.home {
    box-sizing: border-box;
    height: 100%;
    // background: linear-gradient(112.61deg, #3c74eb 15.3%, #95d5f4 89.95%);
    overflow: scroll;
    // padding-bottom: v-bind(paddingBottom);
}

.wrap {
    // background-position: center;
    // background-position-y: -42px;
    // background-size: contain;
    // margin-bottom: 24px;
    padding-top: 52px;
    padding-bottom: 24px;
    background-image: url('@/assets/images/home/<USER>');
    background-repeat: no-repeat;
    background-size: 100% 100%;
}

.home-top{
    padding-left: 16px;
    padding-right: 16px;
    height: 1rem;
    margin-bottom: 4px;
}

.home-content{
    padding-left: 16px;
    padding-right: 16px;
    width: 91%;
    margin-bottom: 32px;
}

.home-spec{
    padding-left: 16px;
    padding-right: 16px;
    width: 91%;
    margin-bottom: 32px;
}
</style>
