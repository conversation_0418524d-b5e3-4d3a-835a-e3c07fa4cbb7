<template>
    <div class="display-flex space-between top-bottom-center">
        <div
            class="font-14 tb-padding-8 lr-padding-12 color-orange  back-color-2b-gradient-brown-10 font-weight-600 border-radius-8">
            精准放款，授信激活额度</div>
        <div class="text-center">
            <div class="font-24 font-bold color-orange ">5000+</div>
            <div class="font-14 color-three-grey">企业成功融资</div>
        </div>
    </div>
    <div v-for="item in matchList" :key="item.name" class="display-flex back-color-2b-gradient-brown-10
         border-radius-8 tb-padding-10 lr-padding-12 t-margin-8 space-between top-bottom-center gap-20">
        <div>
            <div class="display-flex top-bottom-center">
                <div class="font-16"> {{ item.name }}</div>
                <div class="border font-12 tb-padding-4  lr-padding-12 color-three-grey l-margin-16 text-nowrap" style="color: #666666;">匹配度1%
                </div>
            </div>
            <div class="display-flex t-margin-10 gap-8">
                <div class="border font-12 tb-padding-4 lr-padding-12 color-three-grey">
                    <div class="color-orange">500万</div>
                    <div style="color: #666666;">最高额度</div>
                </div>
                <div class="border font-12 tb-padding-4 lr-padding-12 color-three-grey">
                    <div class="color-orange">0.02%</div>
                    <div style="color: #666666;">最低日利率</div>
                </div>
            </div>
        </div>
        <div>
            <div class="orange-btn" @click="toSeekHelp">领取额度</div>
        </div>

    </div>
</template>

<script setup lang="ts">
import { ref, inject, onMounted } from 'vue'
import type { Ref } from 'vue'

import crmService from '@/service/crmService'
import type { IGoodsProductItem } from '@/types/lead'

import { useRouter } from 'vue-router'


const router = useRouter()
const toSeekHelp = () => {
    router.push({
        name: 'customerHelp'
    })
}


const socialCreditCode = inject('socialCreditCode') as Ref<string>

const matchList: Ref<IGoodsProductItem[]> = ref([])
const getList = () => {
    crmService.goodsFinanceEntMatchRule({ socialCreditCode: socialCreditCode.value }).then((res) => {
        console.log('获取产品匹配列表结果', res)
        matchList.value = res.map((item: IGoodsProductItem) => { return { ...item, ...item.matchScore, name: item.name } }).slice(0, 10)
    })
}
onMounted(() => {
    getList()
})
</script>

<style scoped></style>