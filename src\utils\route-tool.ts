import type { RouteRecordRaw } from 'vue-router'

function getKeepAliveRouteNames(routes: RouteRecordRaw[]): string[] {
    const names: string[] = []

    routes.forEach((route) => {
        if (route.meta?.keepAlive === true && route.name) {
            names.push(route.name as string)
        }

        if (route.children) {
            names.push(...getKeepAliveRouteNames(route.children))
        }
    })

    return names
}
export { getKeepAliveRouteNames }
