import http from '@/axios'
import type { IUserAccountInfoResponse, IPageUserListResponse } from '@/types/user'

export default {
    userGetAccountInfo(): Promise<IUserAccountInfoResponse> {
        return http.get(`/api/zhenqi-system/user/get-account-info`)
    },
    userListByName(body: { nickname: string }): Promise<IPageUserListResponse> {
        return http.get(`/api/zhenqi-system/user/list-by-name`, {
            params: body,
            hideError: true,
        })
    },
}
