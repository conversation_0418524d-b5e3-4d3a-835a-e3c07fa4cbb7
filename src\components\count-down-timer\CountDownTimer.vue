<template>
    <div>{{ formattedTime }}</div>
</template>

<script lang="ts" setup>
import { ref, onMounted, onUnmounted, computed, watch } from 'vue'

interface Props {
    // 起始时间，格式为 'YYYY-MM-DD HH:mm:ss'
    startTime: string
    // 倒计时总分钟数
    durationMinutes: number
    // 显示格式：'auto' | 'dhm' | 'hms' | 'ms'
    displayFormat?: 'auto' | 'dhm' | 'hms' | 'ms'
    timeEnded?: () => void
}

const props = withDefaults(defineProps<Props>(), {
    displayFormat: 'auto',
})

const remainingTime = ref<number>(0) // 剩余时间（秒）
let timer: number | null = null

// 计算结束时间
const endTime = computed<Date>(() => {
    const start = new Date(props.startTime)
    return new Date(start.getTime() + props.durationMinutes * 60 * 1000)
})

// 根据剩余时间自动选择最佳显示格式
const getAutoFormat = (seconds: number): string => {
    const days = Math.floor(seconds / 86400)
    const hours = Math.floor((seconds % 86400) / 3600)

    if (days > 0) {
        return 'dhms'
    } else if (hours > 0) {
        return 'hms'
    } else {
        return 'ms'
    }
}

// 格式化时间为不同格式
const formattedTime = computed<string>(() => {
    const totalSeconds = remainingTime.value
    const format = props.displayFormat === 'auto' ? getAutoFormat(totalSeconds) : props.displayFormat

    const days = Math.floor(totalSeconds / 86400)
    const hours = Math.floor((totalSeconds % 86400) / 3600)
    const minutes = Math.floor((totalSeconds % 3600) / 60)
    const seconds = totalSeconds % 60

    if (isNaN(days) || isNaN(hours) || isNaN(minutes) || isNaN(seconds)) {
        return '--:--'
    }

    if (format === 'dhms')
        return `${days}天 ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    if (format === 'dhm') return `${days}天 ${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
    if (format === 'hms')
        return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    if (format === 'ms') return `${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`
    return ''
})

// 更新剩余时间
const updateRemainingTime = (): void => {
    const now = new Date()
    const diff = endTime.value.getTime() - now.getTime()

    if (diff <= 0) {
        remainingTime.value = 0
        if (timer) {
            clearInterval(timer)
            timer = null
        }
        if (props.timeEnded) {
            props.timeEnded()
        }
    } else {
        remainingTime.value = Math.floor(diff / 1000)
    }
}

// 监听时间变化
watch(remainingTime, (newVal) => {
    if (newVal <= 0 && timer) {
        clearInterval(timer)
        timer = null
    }
})

onMounted((): void => {
    updateRemainingTime()
    timer = window.setInterval(updateRemainingTime, 1000)
})

onUnmounted((): void => {
    if (timer) {
        clearInterval(timer)
    }
})
</script>

<style scoped></style>
