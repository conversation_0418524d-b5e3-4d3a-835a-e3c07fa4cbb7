
import type { IAllRecord } from './record'
export interface getCompareIndicatorDataParams extends IAllRecord {
    ids: string
    socialCreditCode: string
}

export interface getIndicatorResultParams extends IAllRecord {
    tableName: string
    socialCreditCode: string
    type: string
    pageNum: number
    pageSize: number
}

export interface getIndicatorResultRecordItem {
    value: string
    type: string
    socialCreditCode: string
    indicator: string
}

export interface getIndicatorResultResponse {
    records: getIndicatorResultRecordItem[]
}

export interface IIndicatorItem {
    currentTime: string[] | [string, string, number][]
    dataId: string[]
    headers: string[]
    id: number | number
}

export interface getCompareIndicatorDataResponse {
    [key: number]: IIndicatorItem[]
}




export interface companyBasicData {
    dataList: { total: number, indicatorName: string, level: number, oneTotal: number }[],
    desc: string[],
    highRisk: number,
    riskNum: number
}

export interface companyFinanceData {
    dataList: {
        causation: string,
        deviationValue: string,
        indexValue: string,
        indicatorName: string,
        interval: string,
        level: number | null,
        referenceValue: string
    }[],
    desc: string[],
    highRisk: number,
    riskNum: number,
    noCollect: boolean
}

export interface companyInvoiceData {
    dataList: {
        indicatorName: string,
        interval: string,
        level: number | null,
        riskEvent: string[],
        riskPoint: string
    }[],
    desc: string[],
    highRisk: number,
    riskNum: number,
    noCollect: boolean,
    invoiceScore: number | null,
    quota: string
}

export interface companyTaxData {
    dataList: {
        causeSpeculation: string,
        deviate: string,
        level: number | null,
        reference: string,
        riskName: string,
        value: string,
        year: string
    }[],
    description: string[],
    highRisk: number,
    riskNum: number,
    noCollect: boolean,
}

export interface companyRelatedData {
    dataList: {
        associatedType: string,
        list: string[]
    }[],
    description: string[],
    highRisk: number,
    riskNum: number,
    totalNum: number
}

export interface companyInspectionData {
    basic: {
        dataList: { total: number, indicatorName: string, level: number, oneTotal: number }[],
        desc: string[],
        highRisk: number,
        riskNum: number
    },
    defeatRatio: string,
    entStatus: string,
    evaluate: string,
    finance: {
        dataList: {
            causation: string,
            deviationValue: string,
            indexValue: string,
            indicatorName: string,
            interval: string,
            level: number | null,
            referenceValue: string
        }[],
        desc: string[],
        highRisk: number,
        riskNum: number,
        noCollect: boolean
    },
    invoice: {
        dataList: {
            indicatorName: string,
            interval: string,
            level: number | null,
            riskEvent: string[],
            riskPoint: string
        }[],
        desc: string[],
        highRisk: number,
        riskNum: number,
        noCollect: boolean,
        invoiceScore: number | null,
        quota: string
    },
    operateState: string,
    rate: string,
    tax: {
        dataList: {
            causeSpeculation: string,
            deviate: string,
            level: number | null,
            reference: string,
            riskName: string,
            value: string,
            year: string
        }[],
        description: string[],
        highRisk: number,
        riskNum: number,
        noCollect: boolean,
    },
    related: {
        dataList: {
            associatedType: string,
            list: string[]
        }[],
        description: string[],
        highRisk: number,
        riskNum: number,
        totalNum: number
    },
    totalNum: number
}