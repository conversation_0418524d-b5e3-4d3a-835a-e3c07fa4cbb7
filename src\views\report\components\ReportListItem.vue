<template>
    <div class="width-100 t-margin-10 gap-10 back-color-white lr-padding-12 tb-padding-12 border border-radius-4 display-flex top-bottom-center"
         v-for="(item, index) in collectList" :key="index">
        <div class="img-box">
            <img class="width-100" src="@/assets/images/download-pdf.png" alt="">
        </div>
        <div class="flex-1">
            <div class="font-14" v-if="type === 'fpbg'">企业发票经营分析报告</div>
            <div class="font-14" v-if="type === 'swbg'">企业财税经营分析报告</div>
            <div class="font-12 color-two-grey t-margin-4">报告时间：{{
                item.reportDate || '-'
            }}</div>
        </div>
        <div class="icon-box display-flex top-bottom-center">
            <SendReportEmail :item="item" />
        </div>
    </div>
    <van-empty v-if="!collectList.length" :image="noDataImg" image-size="224" description="暂无报告" />
</template>

<script lang='ts' setup>
import { ref, onMounted, defineProps, watch } from 'vue'
import type { Ref } from 'vue'
import type { ReportRecordItem } from '@/types/report'

import noDataImg from '@/assets/images/no-data.png'
import SendReportEmail from '@/views/report/components/SendReportEmail.vue'

const props = defineProps<{
    list?: ReportRecordItem[],
    type: string,
    socialCreditCode: string
}>()


watch(() => props.list, (val) => {
    console.log('watch', val)
    collectList.value = val || []
})

const collectList: Ref<ReportRecordItem[]> = ref([])

// const sendEmailFlag: Ref<boolean> = ref(false)

// const email: Ref<string> = ref('')

// const ckLog: Ref<SearchCollectLogItem> = ref('') as SearchCollectLogItem
// const sendEmail = (item: SearchCollectLogItem) => {
//     sendEmailFlag.value = true
//     ckLog.value = item
// }

// const emailForm = ref()

// const confirmEmail = (values: { email: string }) => {
//     reportService.sendEmailReport({
//         socialCreditCode: props.socialCreditCode,
//         reportId: ckLog.value?.requestId || '',
//         taxRequestId: ckLog.value?.requestId || '',
//         email: values.email,
//         reportType: props.type,
//         companyName: ckLog.value?.companyName || '',
//     }).then(() => {
//         showToast('发送成功')
//         sendEmailFlag.value = false
//     })
// }


onMounted(() => {
    collectList.value = props.list || []
})

</script>

<style lang='scss' scoped>
.img-box {
    width: 32px;
    height: 32px;

}

.icon-box {
    width: 50px
}
</style>