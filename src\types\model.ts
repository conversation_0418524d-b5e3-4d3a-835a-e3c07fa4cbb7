
import type { IAllRecord } from './record'
export interface IModelJson {
    type: string;
    key: string;
    model: string;
    options: {
        hidden: boolean;
        customClass?: string
    };
    name: string;
    list?: IAllRecord[]
}

export interface IHighSearchRuleItem {
    dataType: string;
    enums: Array<{
        name: string;
        tagValue: string | number
    }>;
    key: string;
    name: string;
    needSearch?: string;
    unit?: string
    levelConfig?: {
        levels: { name: string, title: string }[]
    }
}

export interface IHighSearchRules {
    name: string;
    children: Array<IHighSearchRuleItem>
}
export interface ISearchConditions2 {
    type?: string;//branch leaf 是分组还是单一条件，branch为单一
    operator: string;
    prop: string; //字段名
    name?: string;
    propLabel?: string;//字段展示名
    value: (string | number | string[])[] | string | number;
    children?: ISearchConditions[] | ISearchConditions2;
    dataType?: string;
    valueLabel?: Array<string | number>
    unit?: string
    matching?: string
    id?: string
    maxScore?: number
}


export interface ISearchConditions {
    type?: string;//branch leaf 是分组还是单一条件，branch为单一
    operator: string;
    prop: string; //字段名
    name?: string;
    propLabel?: string;//字段展示名
    value: (string | number | string[])[] | string | number;
    children?: ISearchConditions2[];
    dataType?: string;
    valueLabel?: Array<string | number>
    unit?: string
    matching?: string
    id?: string
    maxScore?: number
}

export interface IStaticConfigResponse {
    name?: string
}

