<template>
    <div>
        <!-- 标题 -->
        <div class="font-14 font-bold" :style="{ color: mainColor.color }">体检摘要</div>
        <!-- 内容 -->
        <div class="font-12 t-margin-12" :style="{ color: mainColor.color }">
            <div class="display-flex" :class="index < desc.length - 1 ? 'b-margin-10' : ''"
                 v-for="(item, index) in desc" :key="index">
                <div class="index-num r-margin-8  font-12 border display-flex top-bottom-center left-right-center"
                     :style="{ color: mainColor.color, borderColor: mainColor.color }">{{ index + 1 }}</div>
                <div class="font-12">{{ item }}</div>
            </div>
        </div>


    </div>
</template>

<script setup>
import { defineProps, inject } from 'vue'
defineProps({
    desc: {
        type: Array,
    },
    backClass: String
})

const mainColor = inject('mainColor')

console.log(mainColor)
</script>

<style lang="scss" scoped>
.index-num {
    height: 14px;
    width: 14px;
    min-width: 14px;
    border-radius: 14px;
}

.red-border {
    border: 1rpx solid #EC706E;
}

.white-border {
    border: 1rpx solid #fff;
}
</style>