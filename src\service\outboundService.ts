import http from '@/axios'
import type {
    IAutoDialerCompanyDetailRequest,
    IAutoDialerDeleteRequest,
    IAutoDialerPauseRequest,
    IAutoDialerRequest,
    IAutoDialerRespones,
    IAutoDialerRobotListResponse,
    IAutoDialerStartRequest,
    IAutoDialerStrategyListResponse,
    IAutoDialerTaskCallContentRequest,
    IAutoDialerTaskCallContentResponse,
    IAutoDialerTaskCallDetailRequest,
    IAutoDialerTaskCallDetailResponse,
    IAutoDialerTaskDetailInfoItem,
    IAutoDialerTaskDetailRequest,
    IAutoDialerTaskDetailRespones,
    IAppSaveRequest,
    IAppConfigRequest,
    IAppConfigRespones,
    IAutoDialerTaskExportRequest,
    IAutoDialerTaskSaveRequest,
    IAutoDialerTerminationRequest,
    ITaskQueryContactTagCountRequest,
    ITaskQueryContactTagCountResponse,
} from '@/types/autoDialer'
import type { ICommonResponse } from '@/types/axios'
import type { AxiosResponse } from 'axios'

export default {
    taskPage(body: IAutoDialerRequest): Promise<IAutoDialerRespones> {
        return http.get(`/api/zhenqi-outbound/task/page`, {
            params: body,
            repeatCancel: true,
            hideError: true,
        })
    },
    taskDetail(body: IAutoDialerTaskDetailRequest): Promise<IAutoDialerTaskDetailInfoItem> {
        return http.get(`/api/zhenqi-outbound/task/detail`, {
            params: body,
            repeatCancel: true,
        })
    },
    // 客户列表
    taskDetailPage(body: IAutoDialerTaskDetailRequest): Promise<IAutoDialerTaskDetailRespones> {
        return http.get(`/api/zhenqi-outbound/task/detail-page`, {
            params: body,
            repeatCancel: true,
            hideError: true,
        })
    },
    taskCallDetail(body: IAutoDialerTaskCallDetailRequest): Promise<IAutoDialerTaskCallDetailResponse> {
        return http.get(`/api/zhenqi-outbound/task/call-detail`, {
            params: body,
            repeatCancel: true,
            hideError: true,
        })
    },
    taskCallContent(body: IAutoDialerTaskCallContentRequest): Promise<IAutoDialerTaskCallContentResponse> {
        return http.get(`/api/zhenqi-outbound/task/call-content`, {
            params: body,
            repeatCancel: true,
            hideError: true,
        })
    },
    taskCallDownloadRecordingFile(body: IAutoDialerTaskCallContentRequest) {
        return http.get(`/api/zhenqi-outbound/task/call-download-recording-file`, {
            params: body,
            repeatCancel: true,
            hideError: true,
            responseType: 'arraybuffer',
            fullRes: true,
        })
    },
    taskDelete(data: IAutoDialerDeleteRequest) {
        return http.delete('/api/zhenqi-outbound/task/delete', {
            params: data,
            hideError: true,
        })
    },
    taskTermination(data: IAutoDialerTerminationRequest): Promise<ICommonResponse> {
        return http.post(`/api/zhenqi-outbound/task/termination`, data, {
            hideError: true,
        })
    },
    taskStart(data: IAutoDialerStartRequest): Promise<ICommonResponse> {
        return http.post(`/api/zhenqi-outbound/task/start`, data, {
            hideError: true,
        })
    },
    taskPause(data: IAutoDialerPauseRequest): Promise<ICommonResponse> {
        return http.post(`/api/zhenqi-outbound/task/pause`, data, {
            hideError: true,
        })
    },
    taskExport(data: IAutoDialerTaskExportRequest): Promise<AxiosResponse<Blob>> {
        return http.post(`/api/zhenqi-outbound/task/export`, data, {
            hideError: true,
            fullRes: true,
            responseType: 'blob',
        })
    },
    // 企业列表
    taskCompanyDetail(body: IAutoDialerCompanyDetailRequest): Promise<IAutoDialerTaskDetailRespones> {
        return http.get(`/api/zhenqi-outbound/task/company-detail`, {
            params: body,
            repeatCancel: true,
            hideError: true,
        })
    },
    taskSave(data: IAutoDialerTaskSaveRequest): Promise<ICommonResponse> {
        return http.post(`/api/zhenqi-outbound/task/save`, data, {
            hideError: true,
        })
    },
    strategyList(): Promise<IAutoDialerStrategyListResponse> {
        return http.get(`/api/zhenqi-outbound/strategy/list`, {
            repeatCancel: true,
            hideError: true,
        })
    },
    robotList(): Promise<IAutoDialerRobotListResponse> {
        return http.get(`/api/zhenqi-outbound/robot/list`, {
            hideError: true,
        })
    },
    taskEdit(data: IAutoDialerTaskSaveRequest): Promise<ICommonResponse> {
        return http.put(`/api/zhenqi-outbound/task/edit`, data, {
            hideError: true,
        })
    },
    taskQueryContactTagCount(data: ITaskQueryContactTagCountRequest): Promise<ITaskQueryContactTagCountResponse> {
        return http.post(`/api/zhenqi-outbound/task/query-contact-tag-count`, data, {
            hideError: true,
        })
    },
    // 保存应用信息
    appSave(body: IAppSaveRequest) {
        return http.post(`/api/zhenqi-outbound/app/save`, body)
    },
    // 获取应用信息
    appConfig(body: IAppConfigRequest): Promise<IAppConfigRespones> {
        return http.get(`/api/zhenqi-outbound/app/config`, {
            params: body,
            hideError: true,
        })
    },
}
