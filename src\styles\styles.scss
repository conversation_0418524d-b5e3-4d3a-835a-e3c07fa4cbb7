//样式类，如pointer等
.pointer {
    cursor: pointer;
}

.box-shadow {
    box-shadow: 0px 4px 12px rgba(25, 102, 255, 0.16);
}

.border {
    border: 1px solid var(--border-color);
}

.border-radius-4 {
    border-radius: 4px;
}

.absolute {
    position: absolute;
}

.relative {
    position: relative;
}

@for $i from 0 through 100 {
    .left-#{$i} {
        left: #{$i}px;
    }

    .right-#{$i} {
        right: #{$i}px;
    }

    .top-#{$i} {
        top: #{$i}px;
    }

    .bottom-#{$i} {
        bottom: #{$i}px;
    }
}


.fixed {
    position: fixed;
}

.text-ellipsis {
    overflow: hidden;
    text-overflow: ellipsis;
}

.text-decoration {
    text-decoration: line-through;
}

.overflow-y-auto {
    overflow-y: auto;
}

.text-nowrap {
    white-space: nowrap;
}

.text-center {
    text-align: center;
}

.text-right {
    text-align: right;
}

.display-flex {
    display: flex;
}

.display-inline {
    display: inline-block;
}

.display-flex-nowrap {
    display: flex;
    flex-wrap: nowrap;
}

.flex-wrap {
    flex-wrap: wrap;
}

.justify-end {
    justify-content: flex-end;
}

.space-between {
    -webkit-box-pack: justify;
    -webkit-justify-content: space-between;
    -ms-flex-pack: justify;
    justify-content: space-between;
}

.space-around {
    justify-content: space-around;
}

.flex-nowrap {
    flex-flow: nowrap;
}

.top-bottom-center {
    align-items: center;
}

.center {
    align-items: center;
    justify-content: center;
}

.justify-flex-end {
    justify-content: flex-end;
}

.left-right-center {
    justify-content: center;
}

.flex-column {
    flex-direction: column;
}

.flex-row-reverse {
    flex-direction: row-reverse;
}

.flex-1 {
    flex: 1;
}

.flex-2 {
    flex: 2;
}

.flex-3 {
    flex: 3;
}

.flex-grow-1 {
    flex-grow: 1;
}

.flex-grow-2 {
    flex-grow: 2;
}

@for $i from 1 through 100 {
    .gap-#{$i} {
        gap: #{$i}px;
    }
}

.flex-center {
    display: flex;
    justify-content: center;
    align-items: center;
}

.flex {
    display: flex;
}

.pointer {
    cursor: pointer;
}

.not-allow {
    cursor: not-allowed;
}

.split-line {
    border-top: 1px solid var(--border-color);
    height: 0px;
    width: 100%;
}

// overflow hidden
.oh {
    overflow: hidden;
}

// overflow auto
.oa {
    overflow: auto;
}

.boder-none {
    border: none;
}

.icon {
    width: 1em;
    height: 1em;
    vertical-align: -0.15em;
    fill: currentColor;
    overflow: hidden;
}

.border-color-blue {
    border: 1px solid var(--main-blue-);
}

[class*='!border-color-blue'] {
    border: 1px solid var(--main-blue-) !important;
}

.border-trans {
    border: 1px solid transparent;
}

.no-select {
    user-select: none;
}

.border-bottom {
    border-bottom: 1px solid var(--border-color);
}

.border-right {
    border-right: 1px solid var(--border-color);
}

.border-box {
    box-sizing: border-box;
}

.btn {
    background: linear-gradient(to right, #3C74EB, #95D5F4);
    color: var(--main-white);
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    cursor: pointer;
    -webkit-appearance: none;
    text-align: center;
    box-sizing: border-box;
    outline: none;
    margin: 0;
    transition: 0.1s;
    font-weight: 500;
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 4px;
    border: none;

    &:hover {
        opacity: 0.5;
    }
}

.light-btn {
    background-color: rgba(60, 116, 235, 0.05);
    color: var(--main-blue-);
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    -webkit-appearance: none;
    text-align: center;
    box-sizing: border-box;
    outline: none;
    margin: 0;
    transition: 0.1s;
    font-weight: 500;
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 4px;
    border: 1px solid var(--main-blue-);

    &:hover {
        opacity: 0.5;
    }
}

.orange-btn {
    background: linear-gradient(to right, #BA7633, #EACCAB);
    color: var(--main-white);
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    cursor: pointer;
    -webkit-appearance: none;
    text-align: center;
    box-sizing: border-box;
    outline: none;
    margin: 0;
    transition: 0.1s;
    font-weight: 500;
    padding: 6px 12px;
    font-size: 14px;
    border-radius: 4px;
    border: none;

    &:hover {
        opacity: 0.5;
    }
}

em {
    color: var(--main-blue-);
    font-style: normal;
}

.no-focus-visible:focus-visible {
    outline: none;
}

.noselect {
    -webkit-touch-callout: none;
    -webkit-user-select: none;
    -khtml-user-select: none;
    -moz-user-select: none;
    -ms-user-select: none;
    user-select: none;
}

.el-border {
    border: 1px solid var(--el-border-color);
}

// row-gap 控制上下间距
@for $i from 1 through 100 {
    .row-gap-#{$i} {
        row-gap: #{$i}px;
    }
}

// column-gap 控制上下间距
@for $i from 1 through 100 {
    .col-gap-#{$i} {
        column-gap: #{$i}px;
    }
}

.text-right {
    text-align: right;
}

.flex-end {
    justify-content: flex-end;
}

.menu-side-cust-popper {
    border: none !important;
}

.menu-side-cust-popper ul {
    padding: 8px;
    min-width: 120px;
}

.menu-side-cust-popper ul li:last-child {
    margin-bottom: 0px !important;
}

.menu-side-cust-popper .cust-menu-item-label {
    padding-left: 12px;
}

@for $i from 1 through 10 {
    .op-#{$i} {
        opacity: calc($i/10);
    }
}

.img-cover {
    object-fit: cover;
}

.baseline {
    align-items: baseline;
}

.flex-row-end {
    justify-content: flex-end;
}

.flex-col-end {
    align-items: end;
}

.small-primary-btn {
    background: linear-gradient(to right, #3C74EB, #95D5F4);
    color: var(--main-white);
    display: flex;
    justify-content: center;
    align-items: center;
    white-space: nowrap;
    cursor: pointer;
    -webkit-appearance: none;
    text-align: center;
    box-sizing: border-box;
    outline: none;
    margin: 0;
    transition: 0.1s;
    font-weight: 500;
    padding: 6px 12px;
    font-size: 12px;
    border-radius: 4px;
    border: none;
    line-height: 16px;

    &:hover {
        opacity: 0.5;
    }
}

.small-default-btn {
    background-color: var(--main-white);
    outline: 1px solid var(--border-color);
    border-radius: 4px;
    padding: 6px 12px;
    color: var(--two-grey);
    font-size: 12px;
    line-height: 16px;

}

.line-through {
    text-decoration: line-through;
}