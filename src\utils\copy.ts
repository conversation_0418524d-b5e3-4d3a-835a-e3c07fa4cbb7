export const copyToClipboard = (value: string): boolean => {
    let textarea: HTMLTextAreaElement | null = null // 初始化为 null
    let result: boolean | null

    try {
        textarea = document.createElement('textarea')
        textarea.setAttribute('readonly', 'true')
        textarea.setAttribute('contenteditable', 'true')
        textarea.style.position = 'fixed' // prevent scroll from jumping to the bottom when focus is set.
        textarea.value = value

        document.body.appendChild(textarea)

        textarea.focus()
        textarea.select()

        const range: Range = document.createRange()
        range.selectNodeContents(textarea)

        const sel: Selection | null = window.getSelection()
        if (sel) {
            sel.removeAllRanges()
            sel.addRange(range)
        }

        textarea.setSelectionRange(0, textarea.value.length)
        result = document.execCommand('copy')
    } catch (err) {
        console.error(err)
        result = null
    } finally {
        if (textarea && document.body.contains(textarea)) {
            document.body.removeChild(textarea)
        }
    }

    // manual copy fallback using prompt
    if (!result) {
        const isMac: boolean = navigator.platform.toUpperCase().indexOf('MAC') >= 0
        const copyHotkey: string = isMac ? '⌘C' : 'CTRL+C'
        result = !!prompt(`Press ${copyHotkey}`, value)
        if (!result) {
            return false
        }
    }
    return true
}
