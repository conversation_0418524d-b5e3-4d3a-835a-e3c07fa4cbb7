import { createRouter, createWebHistory, type Router, type RouteRecordRaw } from 'vue-router'
import { beforeEachGuard, afterEachGuard } from './guards' // 导入路由守卫
import authRoutes from '@/router/routes/authRoutes'
import clientRoutes from '@/router/routes/clientRoutes'

import { getKeepAliveRouteNames } from '@/utils/route-tool'
// 合并所有路由配置
const routes: Array<RouteRecordRaw> = [...authRoutes, ...clientRoutes]

const router: Router = createRouter({
    history: createWebHistory(),
    routes,
})

router.beforeEach(beforeEachGuard)
router.afterEach(afterEachGuard)

const keepAliveRouteNames = getKeepAliveRouteNames(routes)
export { keepAliveRouteNames }
export default router
