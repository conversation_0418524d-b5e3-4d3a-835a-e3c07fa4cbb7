import type { ICommonResponse } from './axios'
import type { IAllRecord } from './record'

export interface IRoleListRequest extends IAllRecord {
    roleName?: string
    tenantId?: string
}

export interface IRoleListResponse extends ICommonResponse {
    data: IRoleItem[]
}

export interface IRoleItem {
    id: string
    tenantId: string
    roleId: string
    roleName: string
    menuIds: string[]
    scopeData: IRoleScopeData[]
}

export interface IRoleScopeData {
    dataKey: string
    scopeName: string
    scopeType: number
}

export interface IRoleItem {
    id: string
    menuIds: string[]
    roleId: string
    roleName: string
    scopeData: IRoleScopeData[]
    tenantId: string
}
export interface IAddRoleItem {
    id?: string
    menuIds?: string[]
    roleId?: string
    roleName?: string
    scopeData?: IRoleScopeData[]
    tenantId?: string
}

export interface IRoleResponse {
    errCode: number
    errMsg: string
    success: boolean
}
type DataScopeValuesItem = {
    name: string
    value: number
}

export interface IDataScopeListItem {
    dataKey: string
    id: string
    resourceCode: string
    scopeName: string
    scopeType: number
    values: DataScopeValuesItem[]
}

export interface IRoleEditRoleNameParams {
    id: string
    roleName: string
}

export interface IDelRoleParams extends IAllRecord{
    roleId: string
}
