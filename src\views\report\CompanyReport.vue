<template>
    <div class="report-container">
        <van-tabs v-model:active="activeType" @change="tabChange" :sticky="true" :line-width="'50%'"
                  :swipeable="!oemInfo?.pyr">
            <van-tab :title="report.name" :name="report.key" v-for="report in reportList" :key="report.key">
                <div class="back-color-common report-list-box lr-padding-16 tb-padding-16">
                    <div class="font-14 color-two-grey">
                        剩余额度：{{ report.lessEdu }} 份
                    </div>
                    <ReportListItem class="b-padding-40" :list="collectList" :type="report.key"
                                    :socialCreditCode="socialCreditCode" :isBuy="report.isBuy" />

                    <!-- <div style="height: 100px"></div> -->
                    <div class="fixed collect-btn">
                        <div class="btn width-100" style="height: 1rem;" @click="getCollectUrl(report.key)">{{
                            !collectList.length
                                ? '授权' : '更新' }}报告
                        </div>
                    </div>
                </div>
            </van-tab>
        </van-tabs>
    </div>
    <CollectPopup ref="collectPopup" />
    <EmailPopup ref="emailPopup" />
</template>

<script lang='ts' setup>
import { ref, onMounted, provide, computed } from 'vue'
import { showDialog } from 'vant'

import reportService from '@/service/reportService'
import orderService from '@/service/orderService'
import type { Ref } from 'vue'
import type { ReportItem } from '@/types/report'
import type { ReportRecordItem } from '@/types/report'
import { useRoute, useRouter } from 'vue-router'
import ReportListItem from './components/ReportListItem.vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import CollectPopup from '@/components/collect/CollectPopup.vue'
import EmailPopup from './components/EmailPopup.vue'
const store = useStore<RootState>()

const oemInfo = computed(() => {
    const { oemConfig } = store.state.auth || {}
    const { modules } = oemConfig || {}
    const { config } = modules?.[0] || {}
    return config
})

const router = useRouter()
const route = useRoute()

const socialCreditCode: Ref<string> = ref('')
const companyName: Ref<string> = ref('')

const isPreset = computed(() => {
    const { query } = route
    const { preset } = query || {}
    return preset ? true : false
})
const ZQY_REPORT_PRESET_URL= import.meta.env.VITE_ZQY_REPORT_PRESET_API

provide('socialCreditCode', socialCreditCode)
const emailPopup = ref<InstanceType<typeof EmailPopup>>()
const handleOpenEmailPopup = (item: ReportRecordItem) => {
    emailPopup.value?.showSendEmailVisible?.(item)
}
provide('handleOpenEmailPopup',handleOpenEmailPopup)
const collectList: Ref<ReportRecordItem[]> = ref([])

const tabChange = (type: string) => {
    activeType.value = type
    if (type) {
        getCollectInfo(type)
    } else {
        collectList.value = []
    }
}

const replaceAsterisksWithX = (input: string): string => {
    return input.replace(/\*/g, 'x')
}

const getCollectInfo = (type: string) => {
    if (isPreset.value) {
        console.log('示例数据')
        collectList.value = [
            {
                id:'',
                reportDate:'2025-07-10 14:40:05',
                reportUrl: replaceAsterisksWithX(`${ZQY_REPORT_PRESET_URL}zqy/#/${activeType.value === 'swbg' ? 'csReport' : 'invoiceReport'}?requestId=${socialCreditCode.value}&socialCreditCode=${socialCreditCode.value}&preset=1&fileName=${activeType.value === 'swbg' ? '企业财税经营分析报告' : '企业发票数据分析报告'}-${companyName.value}-${Date.now()}.pdf`),
                requestId: socialCreditCode.value,
                socialCreditCode: socialCreditCode.value,
                reportType: type.toUpperCase(),
                companyName: companyName.value
            }
        ]
    } else {
        reportService
            .getReportRecord({ page: 1, pageSize: 9999, socialCreditCode: socialCreditCode.value, reportType: type })
            .then((res) => {
                console.log(res)
                collectList.value = res.data
            })
    }
}

const loading: Ref<boolean> = ref(false)

const reportList: Ref<ReportItem[]> = ref([])

const getReportList = () => {
    if (isPreset.value) {
        setTimeout(() => {
            reportList.value = [
                {
                    id: '',
                    name: '企业财税经营分析报告',
                    key: 'swbg',
                    isBuy: true,
                    ableDownload: true,
                    lessEdu: 1
                },
                {
                    id: '',
                    name: '企业发票数据分析报告',
                    key: 'fpbg',
                    isBuy: true,
                    ableDownload: true,
                    lessEdu: 88,
                },
            ]
        },1)
    } else {
        loading.value = true
        reportService
            .getReportList({ socialCreditCode: socialCreditCode.value })
            .then((res) => {
                console.log('res123123', res)
                // 当oemInfo.pyr存在时，只保留fpbg报告
                reportList.value = oemInfo.value?.pyr
                    ? res.filter((item) => item.key === 'fpbg')
                    : res.filter((item) => item.key !== 'gqbg')

                for (let report of reportList.value) {
                    orderService.orderServiceStatistics({ serviceKeys: report.key }).then((res) => {
                        console.log('res', res)
                        report.lessEdu = res[0].num
                    })
                }
            })
            .finally(() => {
                loading.value = false
            })
    }
}

const activeType = ref('swbg')

onMounted(() => {
    if (route.query.socialCreditCode) {
        socialCreditCode.value = route.query.socialCreditCode as string
        companyName.value = route.query.companyName as string
        getCollectInfo(activeType.value)
        getReportList()
    } else {
        showDialog({
            title: '提示',
            message: '缺少税号',
        }).then(() => {
            router.back()
        })
    }
})

const collectPopup = ref()
const getCollectUrl = async (key: string) => {
    let isBuy = reportList.value.find(item => item.key === activeType.value)?.isBuy
    let info = {
        socialCreditCode: socialCreditCode.value,
        companyName: companyName.value,
        deductType: key,
        isBuy
    }
    collectPopup.value.getCollectUrl(info)
}


</script>

<style lang='scss' scoped>
.report-list-box {
    height: calc(100vh - 120px);
}

.collect-btn {
    bottom: 32px;
    width: calc(100vw - 32px);
}

.icon {
    width: 25px;
    height: 25px
}

.scan-box {
    background: linear-gradient(98.52deg, #ffcd97 2.65%, #ff8a3c 93.37%);
    color: #fff;
}

.close {
    width: 20px;
    height: 20px;
}

.collectQrcode {

    width: 200px;
    height: 200px;
}

</style>
