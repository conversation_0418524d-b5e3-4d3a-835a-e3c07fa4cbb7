import http from '@/axios'
import type { IGetBasicInfoParams } from '@/types/company'
export default {
    getBasicInfo(body:IGetBasicInfoParams): Promise<{ reportDate: string | null }> {
        return http.get(`/api/company/getBasicInfo`, {
            params: body,
            supportMock: true,
        })
    },
    refresh(data: { socialCreditCode: string; companyName: string }): Promise<{ reportDate: string | null }> {
        return http.post(`/api/company/refresh`, data)
    },
}
