<script lang="ts" setup>
import type { IOrderPaymentListItem } from '@/types/order'
import { formatPrice } from '@/utils/format'
import { computed } from 'vue'

const props = defineProps<{
    detail: IOrderPaymentListItem | null
}>()

const total_bill_amount = computed(() => {
    const { total_bill_amount } = props.detail || {}
    if (!total_bill_amount) return ''
    return total_bill_amount
})
const discountAmount = computed(() => {
    const { total_bill_amount, actual_amount } = props.detail || {}
    if (!total_bill_amount) return 0
    if (!actual_amount) return total_bill_amount
    return total_bill_amount*1 - actual_amount*1
})
</script>

<template>
    <div class="order-info flex flex-column gap-6 back-color-white lr-padding-16 tb-padding-8 border-radius-8">
        <div class="flex flex-row space-between top-bottom-center">
            <div class="font-16 color-black lh-22">费用详情</div>
        </div>
        <div class="flex flex-row space-between top-bottom-center">
            <div class="font-14 color-three-grey lh-20">订单费用</div>
            <div class="flex flex-row baseline gap-2 color-black">
                <div class="font-12 lh-16 flex">¥</div>
                <!-- <div class="font-16 lh-16 font-weight-500">{{ actualAmount ? formatPrice(actualAmount) : '' }}</div> -->
                <div class="font-16 lh-16 font-weight-500">{{ total_bill_amount ? formatPrice(total_bill_amount) : 0 }}</div>
            </div>
        </div>
        <div v-if="discountAmount > 0" class="flex flex-row space-between top-bottom-center">
            <div class="font-14 color-three-grey lh-20">已优惠</div>
            <div class="flex flex-row baseline gap-2 color-red">
                <div class="font-12 lh-16 flex">-¥</div>
                <div class="font-16 lh-16 font-weight-500">{{ formatPrice(discountAmount) }}</div>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.tips {
    color: #ff854c;
}
</style>
