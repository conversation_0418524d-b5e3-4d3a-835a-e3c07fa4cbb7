<script lang="ts" setup>
import type { IOrderPaymentListItem } from '@/types/order'
import { copyToClipboard } from '@/utils/copy'
import { showToast } from 'vant'
import { computed } from 'vue'

const props = defineProps<{
    detail: IOrderPaymentListItem | null
}>()

const outOrderId = computed(() => {
    const { out_order_id } = props.detail || {}
    if (!out_order_id) return ''
    return out_order_id
})

const createdAt = computed(() => {
    const { created_at } = props.detail || {}
    if (!created_at) return ''
    return created_at
})

const doCopy = (value: string) => {
    const res = copyToClipboard(value)
    if (res) {
        showToast('已复制到剪贴板')
    }
}
</script>

<template>
    <div class="order-info flex flex-column gap-6 back-color-white lr-padding-16 tb-padding-8 border-radius-8">
        <div class="flex flex-row space-between top-bottom-center">
            <div class="font-16 color-black lh-22">订单信息</div>
        </div>
        <div class="flex flex-row space-between top-bottom-center">
            <div class="font-14 color-three-grey lh-20">订单编号</div>
            <div class="font-14 color-black lh-20">
                {{ outOrderId }}
                <span @click="doCopy(outOrderId)">
                    <Icon icon="icon-copy-right" :size="14" color="var(--main-black)" />
                </span>
            </div>
        </div>
        <div class="flex flex-row space-between top-bottom-center">
            <div class="font-14 color-three-grey lh-20">创建时间</div>
            <div class="font-14 color-black lh-20">{{ createdAt }}</div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.tips {
    color: #ff854c;
}
</style>
