export const getUrlParams = (url?: string) => {
    const queryString = url ? url.split('?')[1] : window.location.search.substring(1)
    const params: Record<string, string> = {}

    if (queryString) {
        queryString.split('&').forEach((param) => {
            const [key, value] = param.split('=')
            if (key) {
                params[decodeURIComponent(key)] = decodeURIComponent(value || '')
            }
        })
    }

    return params
}
