import { type RouteRecordRaw } from 'vue-router'
import LoginView from '@/views/auth/LoginView.vue'

const authRoutes: Array<RouteRecordRaw> = [
    {
        path: '/login',
        name: 'passwordLogin',
        component: LoginView,
        meta: { requiresAuth: false, title: '登录' },
    },
    {
        path: '/phone-login',
        name: 'phoneLogin',
        component: LoginView,
        meta: { requiresAuth: false, title: '登录' },
    },
    {
        path: '/forgot',
        name: 'forgot',
        component: LoginView,
        meta: { requiresAuth: false, title: '登录' },
    },
    {
        path: '/phone-bind',
        name: 'phoneBind',
        component: LoginView,
        meta: { requiresAuth: false, title: '登录' },
    },
    {
        path: '/phone-validate',
        name: 'phoneValidate',
        component: LoginView,
        meta: { requiresAuth: false, title: '登录' },
    },
]

export default authRoutes
