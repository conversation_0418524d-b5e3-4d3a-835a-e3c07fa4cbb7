<template>
    <Icon icon="icon-mail-03" size="25" color="#3C74EB" @click="() => showSendEmailVisible(props.item)" />
</template>

<script lang='ts' setup>
import type { ReportRecordItem } from '@/types/report'
import Icon from '@/components/common/Icon.vue'
import { inject } from 'vue'

const props = defineProps<{
    item: ReportRecordItem
}>()
const showSendEmailVisible = inject('handleOpenEmailPopup') as (val:ReportRecordItem) => void

</script>
<style lang='scss' scoped></style>