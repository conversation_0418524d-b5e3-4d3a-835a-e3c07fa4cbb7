<script lang="ts" setup>
import POSTER01 from '@/assets/images/home/<USER>'
import POSTER02 from '@/assets/images/home/<USER>'
import POSTER03 from '@/assets/images/home/<USER>'
import POSTER04 from '@/assets/images/home/<USER>'
import POSTER05 from '@/assets/images/home/<USER>'

const images = [POSTER01, POSTER02, POSTER03, POSTER04, POSTER05]
</script>

<template>
    <div class="home-swipe">
        <van-swipe class="home-van-swipe" :autoplay="3000" indicator-color="white">
            <van-swipe-item :autoplay="3000" v-for="(image, index) in images" :key="index">
                <img :src="image" class="width-100 height-100 img-cover" />
            </van-swipe-item>
        </van-swipe>
    </div>
</template>

<style scoped>
.home-swipe {
    height: 141px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 12px;
    padding-left: 16px;
    padding-right: 16px;
}

.home-van-swipe {
    width: 100%;
    height: 100%;
    overflow: hidden;
    border-radius: 8px;
}
</style>
