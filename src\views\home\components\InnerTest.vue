<script lang="ts" setup>
import { copyToClipboard } from '@/utils/copy'
import { computed } from 'vue'
import { useRouter } from 'vue-router'

const router = useRouter()
// const toSearch = () => {
//     router.push({ name: 'searchCompany' })
// }

// const toGetCode = () => {
//     router.push({ name: 'getCode' })
// }

const toGodds = () => {
    router.push({
        name: 'goodsList',
        query: {
            goods: 'leads-group',
        },
    })
}
const toGodds2 = () => {
    router.push({
        name: 'goodsList',
        query: {
            goods: 'combo-group',
        },
    })
}
const toOrders = () => {
    router.push({
        name: 'myOrders',
    })
}
const doCopy = () => {
    copyToClipboard('你大爷')
}

const isProduction = computed(() => {
    return import.meta.env.VITE_APP_ENV === 'production'
})
</script>

<template>
    <div v-if="!isProduction">
        <div @click="toOrders">我的订单</div>
        <div @click="toGodds">线索商品</div>
        <div @click="toGodds2">套餐商品</div>
        <div @click="doCopy">复制内容</div>
    </div>
</template>

<style scoped>
.home-search {
    margin-bottom: 16px;
    padding-left: 16px;
    padding-right: 16px;
}
.icon {
    width: 10px;
    height: 10px;
}
</style>
