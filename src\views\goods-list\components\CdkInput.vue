<script lang='ts' setup>
import { ref, watch } from 'vue'
import { showSuccessToast, showFailToast } from 'vant'
import Icon from '@/components/common/Icon.vue'
import orderService from '@/service/orderService'

const props = defineProps<{
    goodsIds: string,
    originalAmount: number,
    code?: string
}>()
watch(() => props.code, (newVal) => {
    cdkCode.value = newVal || ''
})
const cdkVisible = ref(false)
const cdkCode= ref<string>('')
const handleCancel = () => {
    cdkVisible.value = false
    cdkCode.value = ''
}
const emit = defineEmits(['updateDiscountInfo'])
const handleSendCdk = () => {
    console.log('cdkCode', cdkCode.value)
    if (cdkCode.value) {
        // 效验CDK (商品id 数量 ckd码)
        orderService.orderGoodsValidateCoupon({
            code: cdkCode.value,
            amount: props.originalAmount,
            goodsId: props.goodsIds
        }).then((res) => {
            const { errCode, data, errMsg } = res
            if (errCode === 0) {
                emit('updateDiscountInfo', data)
                showSuccessToast('验证成功')
                handleCancel()
            } else {
                showFailToast('兑换码' + errMsg)
            }
        })
    } else {
        emit('updateDiscountInfo')
        handleCancel()
    }
    
}
</script>
<template>
    <div class="color-two-grey font-14 display-flex top-bottom-center b-margin-8">
        <div>
            <Icon icon="icon-Frame7" size="20" color="#656565"></Icon>
        </div>
        <span class="l-margin-2" @click="cdkVisible = true">使用兑换码</span>
    </div>
    <van-popup
        v-model:show="cdkVisible"
        style="top: 40%; border-radius: 8px;"
        :close-on-click-overlay="false"
    >
        <div class="w-291 back-color-white border-radius-8 lr-padding-16">
            <div class="font-16 font-weight-500 color-black tb-margin-12 text-center relative">兑换码
                <div class="absolute top-0 right-0" @click="handleCancel()">
                    <van-icon name="cross" />
                </div>
            </div>
            <van-form ref="emailForm" required="auto" @submit="handleSendCdk">
                <van-field 
                    v-model="cdkCode"
                    placeholder="请输入兑换码"
                    clearable
                />
                <div class="t-margin-6 font-12 color-three-grey">同一兑换码只能使用一次，核销后失效</div>
                <div class="t-margin-12 b-margin-12">
                    <van-button
                        type="primary"
                        class="width-100 h-36 border-radius-8"
                        style="background: linear-gradient(to right, #3c74eb, #95d5f4); border: none;"
                        native-type="submit">
                        确认使用
                    </van-button>
                </div>
            </van-form>
        </div>
    </van-popup>
</template>
<style scoped lang='scss'>
::v-deep(.van-cell){
    border:1px solid var(--border-color);
}
::v-deep(.van-field__control::placeholder) {
  color: var(--three-grey)
}
</style>
