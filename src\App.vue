<template>
    <div id="app">
        <!-- 全局头部 -->
        <!-- 路由视图 -->
        <!-- <router-view v-slot="{ Component }" v-if="isRouterAlive">
            <keep-alive :include="['MainPage']">
                <component :is="Component" />
            </keep-alive>
        </router-view> -->

        <router-view v-slot="{ Component }" v-if="isRouterAlive">
            <keep-alive :include="keepAliveRouteNames">
                <component :is="Component" />
            </keep-alive>
        </router-view>

        <!-- 全局页脚 -->
    </div>
</template>

<script setup lang="ts">
import { nextTick, onMounted, provide, ref } from 'vue'
import { loadIconfont } from './utils/loadIconfont'
import { loadVconsole } from './utils/load-vconsole'
import { keepAliveRouteNames } from '@/router'
const isRouterAlive = ref(true)

const reload = () => {
    console.log('重载页面')
    isRouterAlive.value = false //先关闭，
    nextTick(() => {
        isRouterAlive.value = true
    })
}

provide('reload', reload)

onMounted(() => {
    // 载入icon资源
    loadIconfont()

    // 载入vconsole
    loadVconsole()
})
</script>
<style>
/* 全局样式 */
#app {
    font-family:
        Inter, 'Helvetica Neue', Helvetica, 'PingFang SC', 'Hiragino Sans GB', 'Microsoft YaHei', 微软雅黑, Arial,
        sans-serif;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    color: #2c3e50;
    margin: 0;
    padding: 0;
    height: 100vh;
}

/* 其他全局样式 */
</style>
