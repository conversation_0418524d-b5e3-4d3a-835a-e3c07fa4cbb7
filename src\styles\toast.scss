
/* Toast容器样式 */
.van-toast {
    gap: 8px !important;
    font-size: 14px !important;
    background-color: rgba(0, 0, 0, 0.7) !important;
    color: white !important;
    border-radius: 8px !important;
    padding: 8px 16px !important;
    /* 使用flex布局但不强制覆盖display属性 */
    justify-content: center !important;
    align-items: center !important;
}

/* 只有当Toast元素存在且可见时才应用flex */
.van-toast:not([style*="display: none"]):not([hidden]) {
    display: flex !important;
}

.van-toast__text {
    font-size: 14px !important;
    color: white !important;
}