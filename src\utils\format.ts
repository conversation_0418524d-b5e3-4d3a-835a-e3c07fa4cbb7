export const maskPhone = (phone: string) => {
    if (!/^1\d{10}$/.test(phone)) return phone // 简单校验一下是不是手机号
    return phone.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
}

export const indexToSlot = (idx: number) => {
    // 参数校验：非负整数
    if (!Number.isInteger(idx) || idx < 0) {
        throw new Error('索引必须是非负整数')
    }

    const MINUTES_PER_SLOT = 30 // 每段分钟数，可改成 15、60…
    const TOTAL_MINUTES = idx * MINUTES_PER_SLOT

    const startHours = Math.floor(TOTAL_MINUTES / 60) % 24
    const startMinutes = TOTAL_MINUTES % 60

    const endMinutesTotal = TOTAL_MINUTES + MINUTES_PER_SLOT
    const endHours = Math.floor(endMinutesTotal / 60) % 24
    const endMinutes = endMinutesTotal % 60

    const pad = (n: number): string => n.toString().padStart(2, '0')

    return `${pad(startHours)}:${pad(startMinutes)}-${pad(endHours)}:${pad(endMinutes)}`
}

export const numbersToTimeRanges = (numbers: number[]) => {
    if (numbers.length === 0) return ''

    // 先对数字进行排序（确保输入是有序的，但这里假设已经有序）
    numbers.sort((a, b) => a - b)

    const ranges: [number, number][] = []
    let start = numbers[0]
    let prev = numbers[0]

    for (let i = 1; i < numbers.length; i++) {
        if (numbers[i] === prev + 1) {
            prev = numbers[i]
        } else {
            ranges.push([start, prev])
            start = numbers[i]
            prev = numbers[i]
        }
    }
    ranges.push([start, prev]) // 添加最后一个范围

    // 将数字范围转换为时间字符串
    const timeRanges = ranges.map(([startNum, endNum]) => {
        const startTime = numberToTime(startNum)
        const endTime = numberToTime(endNum + 1) // 结束时间是下一个数字的开始时间
        return `${startTime}-${endTime}`
    })

    return timeRanges.join(',')
}

const numberToTime = (n: number) => {
    const totalMinutes = (n - 1) * 30
    const hours = Math.floor(totalMinutes / 60) % 24
    const minutes = totalMinutes % 60
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}`
}

/**
 * 将时间字符串 (HH:MM) 转换为对应的数字 (1-48)
 * @param time 时间字符串 (如 "09:00")
 * @returns 对应的数字 (如 19)
 */
function timeToNumber(time: string): number {
    const [hours, minutes] = time.split(':').map(Number)
    const totalMinutes = hours * 60 + minutes
    return Math.floor(totalMinutes / 30) + 1 // 1-48
}

/**
 * 将时间段字符串转换为数字数组
 * @param timeRanges 时间段字符串 (如 "09:00-11:30,13:30-14:00")
 * @returns 数字数组 (如 [19,20,21,22,23,28,29])
 */
export const timeRangesToNumbers = (timeRanges: string): number[] => {
    if (!timeRanges.trim()) return []

    const ranges = timeRanges.split(',').map((s) => s.trim())
    const numbers: number[] = []

    for (const range of ranges) {
        const [startTime, endTime] = range.split('-').map((s) => s.trim())
        const startNum = timeToNumber(startTime)
        const endNum = timeToNumber(endTime) - 1 // 结束时间对应的是下一个数字的开始

        for (let num = startNum; num <= endNum; num++) {
            numbers.push(num)
        }
    }

    return numbers.sort((a, b) => a - b) // 确保升序
}

interface EnumItem<T = string> {
    label: string
    value: T
}

export const convertToEnumLabels = <T extends string | number>(str: string, enumList: EnumItem<T>[]): string => {
    const values = str.split(',')

    const valueToLabelMap = new Map<string, string>()

    enumList.forEach((item) => {
        valueToLabelMap.set(String(item.value), item.label)
    })

    const result = values
        .map((value) => {
            const label = valueToLabelMap.get(value)
            return label
        })
        .filter((label) => label)

    return result.join(',')
}

/**
 * 将时间字符串 (HH:MM) 转换为特定显示格式 (如 "12:41" "昨天" "2天前" "3天前" "1周前" "1月前" "3月前" "1年前")
 * @param time 时间字符串 (如 "2025-06-05 11:41:45")
 * @returns 对应的时间段 (如 "12:41" "昨天" "2天前" "3天前" "1周前" "1月前" "3月前" "1年前")
 */
export const formatDate2Period = (createTime: string): string => {
    const currentDate = new Date()
    const createDate = new Date(createTime)

    // 创建当前日期0点
    const currentStartOfDay = new Date(currentDate)
    currentStartOfDay.setHours(0, 0, 0, 0)

    // 创建目标日期0点
    const createStartOfDay = new Date(createDate)
    createStartOfDay.setHours(0, 0, 0, 0)

    // 计算日历天数差
    const diffTime = currentStartOfDay.getTime() - createStartOfDay.getTime()
    const diffInDays = Math.floor(diffTime / (1000 * 3600 * 24))

    // 判断逻辑保持不变
    if (diffInDays === 0) {
        // 今天
        const hours = createDate.getHours()
        const minutes = createDate.getMinutes()
        return `${hours}:${minutes < 10 ? '0' + minutes : minutes}`
    } else if (diffInDays === 1) {
        // 昨天
        return '昨天'
    } else if (diffInDays < 7) {
        // 2天前到6天前
        return `${diffInDays}天前`
    } else if (diffInDays < 30) {
        // 1周前
        return `${Math.floor(diffInDays / 7)}周前`
    } else if (diffInDays < 365) {
        // 1个月前
        return `${Math.floor(diffInDays / 30)}月前`
    } else {
        // 超过1年
        return `${Math.floor(diffInDays / 365)}年前`
    }
}

export const formatPrice = (amount: number) => {
    return (Number(amount) / 100).toFixed(2)
}

export const removeQueryParam = (paramName: string) => {
    const searchParams = new URLSearchParams(document.location.search)

    // 删除指定的参数
    searchParams.delete(paramName)

    // 构建新的查询字符串
    const newSearch = searchParams.toString()

    return newSearch
}
