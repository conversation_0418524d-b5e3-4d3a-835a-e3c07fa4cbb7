import type { RouteLocationNormalized, NavigationGuardNext } from 'vue-router'
import { getItem } from '@/utils/storage'
import store from '@/store'

export function beforeEachGuard(to: RouteLocationNormalized, from: RouteLocationNormalized, next: NavigationGuardNext) {
    console.log('from: ', from)
    console.log('to: ', to)

    if (to.meta.requiresAuth) {
        const isAuthenticated = checkAuthentication() // 身份检查函数
        if (isAuthenticated) {
            next() // 如果已认证，放行
        } else {
            next('/phone-login') // 未认证，跳转到登录页
        }
    } else {
        next() // 不需要认证，放行
    }
}

export function afterEachGuard() {
    //路由跳转完成以后
}

function checkAuthentication(): boolean {
    const accessToken = getItem('access_token')
    let flag = true

    if (!accessToken) {
        flag = false
    }

    if (flag) {
        store.dispatch('auth/loginSuccess', { access_token: accessToken })
    }

    if (!flag) {
        store.dispatch('auth/logout')
    }

    return flag
}
