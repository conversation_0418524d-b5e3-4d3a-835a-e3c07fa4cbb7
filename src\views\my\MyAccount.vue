<template>
    <div class="home all-padding-16" style="background-color: #FAFAFA;">

        <van-list class="custom-cell">
            <van-cell>
                <div class="display-flex space-between">
                    <span class="font-16 color-two-grey">昵称</span>
                    <span class="font-16 color-black">{{ userInfo?.nickname || '-' }}</span>
                </div>
            </van-cell>
            <van-cell>
                <div class="display-flex space-between">
                    <span class="font-16 color-two-grey">登录账号</span>
                    <span class="font-16 color-black">{{ userInfo?.username || '-' }}</span>
                </div>
            </van-cell>
            <van-cell>
                <div class="display-flex space-between">
                    <span class="font-16 color-two-grey">手机号</span>
                    <span class="font-16 color-black">{{ userInfo?.mobile || '-' }}</span>
                </div>
            </van-cell>
            <van-cell>
                <div class="display-flex space-between">
                    <span class="font-16 color-two-grey">所属组织</span>
                    <span class="font-16 color-black">{{ defaultOrg?.name ? defaultOrg.name : '-' }}</span>
                </div>
            </van-cell>
            <van-cell>
                <div class="display-flex space-between">
                    <span class="font-16 color-two-grey">所属租户</span>
                    <span class="font-16 color-black">{{ tenantInfo?.name ? tenantInfo?.name : '-' }}</span>
                </div>
            </van-cell>

        </van-list>

        <!-- 退出登录按钮 -->
        <div
            class="flex-center all-padding-16 b-margin-4 absolute bottom-0 left-0 right-0"
            :style="{ marginBottom: paddingBottom }"
        >
            <button class="logout-button" @click="handleLogout">退出登录</button>
            <!-- <button v-else class="logout-button" @click="toLogin">
                去登录
            </button> -->
        </div>
    </div>


</template>

<script lang='ts' setup>
import { computed, onMounted } from 'vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'
import { tabbarheight } from '@/utils/tabbar-height'
import { showConfirmDialog } from 'vant'

const defaultOrg = computed(() => {
    const { account } = store.state.user || {}
    const { user, orgs } = account || {}
    const { defaultOrg } = user || {}

    if (!defaultOrg) return null
    if (!orgs || !Array.isArray(orgs) || orgs.length === 0) return null

    const target = orgs.find((org) => org.id === defaultOrg)

    if (!target) return null

    return target
})
const store = useStore<RootState>()
const userInfo = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    return user
})

const tenantInfo = computed(() => {
    const { account } = store.state.user
    const { tenant } = account || {}
    return tenant
})

const paddingBottom = computed(() => {
    return tabbarheight() + 'px'
})

const logout = async () => {
    store.dispatch('auth/logout')
}

const handleLogout = () => {
    console.log('退出登录')
    showConfirmDialog({
        title: '提示',
        message: '确定退出登录吗？',
    }).then(() => {
        logout()
    })
}

onMounted(() => {
    // console.log('MyAccount mounted',userInfo.value)
    // console.log('MyAccount mounted',orgInfo.value)
})

</script>

<style lang='scss' scoped>
.home {
    height: 100%;
    overflow: scroll;
    padding-bottom: v-bind(paddingBottom);
    display: flex;
    flex-direction: column;
}

.custom-cell {
    border-radius: 16px; 
    overflow: hidden; 
}

.logout-button {
    width: 8rem;
    height: 1.2rem;
    background: linear-gradient(to right, #3c74eb, #95d5f4);
    border: none;
    border-radius: 8px;
    color: white;
    font-size: 16px;
    font-weight: 500;
    cursor: pointer;
    transition: opacity 0.3s ease;

    &:hover {
        opacity: 0.9;
    }

    &:active {
        opacity: 0.8;
    }
}
</style>