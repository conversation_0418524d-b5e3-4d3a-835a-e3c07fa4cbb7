import { createApp } from 'vue'
import store from './store'
import App from './App.vue'
import router from './router' // 引入路由
import './styles/main.scss'
import lodash from 'lodash'
import moment from 'moment'
import 'amfe-flexible'
import Vue3Lottie from 'vue3-lottie'
import 'vant/lib/toast/style'
import 'vant/lib/dialog/style'

const app = createApp(App)

app.use(router) // 使用路由
app.use(store) // 使用 Vuex/Pinia

// 在全局挂载 ECharts
app.config.globalProperties.$ssoUrl = import.meta.env.VITE_SSO_BASE_URL
app.config.globalProperties.$moment = moment
app.config.globalProperties.$lodash = lodash

app.use(Vue3Lottie, { name: 'LottieAnimation' })

app.mount('#app') // **只执行一次挂载**
