import http from '@/axios'
import type {
    IAuthCaptchaCheckResponse,
    IAuthForgetPasswordCodeRequest,
    IAuthForgetPasswordCodeResponse,
    IAuthLogin,
    IAuthLoginResponse,
    IAuthMobileCodeRequest,
    IAuthSysResponse,
} from '@/types/auth'
import type { ICommonResponse } from '@/types/axios'
import type { IResProps, ITacProps } from '@/types/tac'
import { parseTime } from '@/utils/parse-time'
import { AxiosHeaders } from 'axios'

export default {
    oauthToken(credentials: IAuthLogin): Promise<IAuthSysResponse> {
        const { captchaKey } = credentials || {}

        const headers = new AxiosHeaders()

        if (captchaKey) {
            headers['Captcha-Key'] = captchaKey
        }

        return http.post(`/api/zhenqi-auth/oauth/token`, credentials, {
            headers: headers,
            unlessAuth: true,
            hideError: true,
        })
    },
    oauthOpenCaptcha(body: { username: string }): Promise<IAuthCaptchaCheckResponse> {
        return http.get(`/api/zhenqi-auth/oauth/open-captcha`, {
            params: body,
            hideError: true,
        })
    },
    oauthCaptcha(credentials: IAuthLogin, fn: (credentials: IAuthLogin) => void, back: () => void) {
        const config = {
            requestCaptchaDataUrl: '/api/zhenqi-auth/oauth/captcha?type=slider',
            validCaptchaUrl: '/api/zhenqi-auth/oauth/captcha-check',
            bindEl: '#captcha',
            validSuccess: (res: IResProps, e?: null, tac?: ITacProps) => {
                if (tac) tac.destroyWindow()
                console.log('验证成功，后端返回的数据为', res)
                const { code, data } = res || {}
                const { id } = data || {}
                if (code === 200 && data) {
                    return fn({ ...credentials, captchaKey: id })
                }
            },
            validFail: (_: IResProps, __?: null, tac?: ITacProps) => {
                console.log('验证码验证失败回调...')

                if (tac) tac.reloadCaptcha()
            },
            btnRefreshFun: (_?: null, tac?: ITacProps) => {
                if (tac) tac.reloadCaptcha()
                if (typeof back === 'function') back()
            },
            btnCloseFun: (_?: null, tac?: ITacProps) => {
                if (tac) tac.destroyWindow()
                if (typeof back === 'function') back()
            },
        }

        window.initTAC('static/tac', config).then((tac) => {
            tac.init()
            window.parseTime = parseTime
        })
    },
    oauthGetForgetPasswordCode(body: IAuthForgetPasswordCodeRequest): Promise<IAuthForgetPasswordCodeResponse> {
        return http.get(`/api/zhenqi-auth/oauth/get-forget-password-code`, {
            params: body,
        })
    },
    switchOrg(body: { orgId: string; grant_type: string }): Promise<IAuthLoginResponse> {
        return http.post(`/api/zhenqi-auth/oauth/switch-org`, body)
    },
    logout(): Promise<IAuthLoginResponse> {
        return http.post(`/api/zhenqi-auth/oauth/logout`, {}, { hideError: true, timeout: 1000 })
    },
    mobileOauthToken(credentials: IAuthLogin): Promise<IAuthSysResponse> {
        const { captchaKey } = credentials || {}

        const headers = new AxiosHeaders()

        if (captchaKey) {
            headers['Captcha-Key'] = captchaKey
        }

        return http.post(`/api/zhenqi-auth/mobile/oauth/token`, credentials, {
            headers: headers,
            unlessAuth: true,
            hideError: true,
        })
    },
    mobileGetLoginCode(body: IAuthMobileCodeRequest): Promise<ICommonResponse> {
        return http.get(`/api/zhenqi-auth/mobile/get-login-code`, {
            params: body,
        })
    },
}
