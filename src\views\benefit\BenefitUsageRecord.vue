<template>
    <div class=" back-color-common all-padding-16 display-flex flex-column height-100">
        <van-empty v-if="!list.length" :image="noDataImg" image-size=100%
                   description="暂无使用明细" >
            <van-button
                v-if="ti !== 0"
                type="primary"
                style="width: 100px;height: 30px;"
                @click="showPopup = true"
            >
                {{ '重新筛选' }}
            </van-button>        
        </van-empty>
        <van-list
            v-model:loading="listLoading"
            :finished="finished"
            @load="listOnload"
        >
            <van-cell class="van-cell-group" v-for="item in recordList" :key="item.id">
                <div class="display-flex top-bottom-center t-padding-8 border-box" style="min-height: 2rem">
                    <div
                        class="flex-1 display-flex flex-column top-bottom-center gap-8"
                        style="align-items: flex-start"
                    >
                        <div class="display-flex top-bottom-center font-16 font-weight-500" style="color: #262626">
                            <span class="font-20 font-weight-600">{{ item.created_at ? item.created_at.split('-')[1] : '-' }}月</span>
                            <Icon
                                v-if="item.isExpanded"
                                class="l-margin-4"
                                icon="icon-a-Frame1171276219"
                                size="16"
                                @click="showPopup = true"
                            />
                        </div>
                        <div class="display-flex space-between top-bottom-center font-16 color-black width-100" >
                            <span>{{ item.service_name }}</span>
                            <div class="display-flex top-bottom-center">
                                -{{ item.number }}
                            </div>
                        </div>
                        <van-list
                            v-model:loading="Loading"
                            finished
                            class="van-list-detail"
                        >
                            <van-cell class="van-cell-detail" v-for="itemDetail in item.extraInfo" :key="itemDetail.id">
                                <div class="display-flex top-bottom-center b-margin-8">
                                    <div 
                                        class="w-46 border-radius-4 back-color-blue color-white font-14 font-weight-500 r-margin-8 border-box lr-padding-8"
                                    >
                                        {{ itemDetail.companyName ? itemDetail.companyName.slice(0,4) : '暂无数据'}}
                                    </div>
                                    <div class="color-two-grey font-14 flex-1" style="text-align: left">
                                        <div class="display-flex space-between">
                                            <div
                                                class="color-black font-16 b-margin-2 maxw-250 text-ellipsis text-nowrap w-200"
                                            >
                                                {{ itemDetail.companyName ? itemDetail.companyName : '-' }}
                                            </div>
                                            <div class="font-12" >
                                                {{ serviceType(itemDetail.change_type) }}
                                            </div>
                                        </div>
                                        <div class="display-flex space-between">
                                            <div>
                                                {{ item.created_at ? item.created_at : '-' }}
                                            </div>
                                            
                                            <div class="font-16 color-black">
                                                -{{ itemDetail.belong }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </van-cell>
                        </van-list>
                    </div>
                </div>
            </van-cell>
        </van-list>
    </div>
    <van-popup
        v-model:show="showPopup"
        position="bottom"
        :style="{ padding: '4px', borderRadius: '8px 8px 0 0'}"  
    >
        <div class="display-flex space-between width-100 ">
            <van-tabs v-model:active="active" style="width: 100%;">
                <van-tab title="月份选择">
                    <van-date-picker
                        v-model="currentDate_month"
                        title="选择年月"
                        :min-date="minDate"
                        :max-date="maxDate"
                        :formatter="formatter"
                        :columns-type="columnsType"
                        :show-toolbar="false"
                    />
                </van-tab>
                <van-tab title="自定义时间">
                    <div class="display-flex flex-column gap-16 font-14 t-margin-16 l-padding-16" style="color: #B2B2B2;width: 100%;">
                        <span>交易时间</span>
                        <div class="display-flex gap-8">
                            <button class="status-btn" :class="{ active: selectedStatus === 'three-month' }"
                                    @click="onClickStatus('three-month')">
                                近三月
                            </button>
                            <button class="status-btn" :class="{ active: selectedStatus === 'half-year' }" @click="onClickStatus('half-year')">
                                近半年
                            </button>
                            <button class="status-btn" :class="{ active: selectedStatus === 'one-year' }" @click="onClickStatus('one-year')">
                                近一年
                            </button>
                        </div>
                        <div class="display-flex space-between">
                            <span>自定义</span>
                            <Icon icon="icon-trash-01" size="20" color="#B2B2B2" @click="clearTime" />
                        </div>
                        <div class="flex-center gap-16">
                            <div class="date-range" @click="onClickDate('startTime')" :class="{ active: activeDate === 'startTime' }">
                                <span>{{ startTime || '开始时间' }}</span>
                                <div class="bottom-line"></div>
                            </div>
                            <span class="font-16 color-black">至</span>
                            <div class="date-range" @click="onClickDate('endTime')" :class="{ active: activeDate === 'endTime' }">
                                <span>{{ endTime || '结束时间' }}</span>
                                <div class="bottom-line"></div>
                            </div>
                        </div>
                        <van-date-picker
                            class="t-margin-12"
                            v-model="currentDate_customize"
                            title="选择日期"
                            :min-date="minDate"
                            :max-date="maxDate"
                            :show-toolbar="false"
                        />
                    </div>
                </van-tab>
            </van-tabs>
            <Icon class="t-margin-10 r-margin-16" icon="icon-x-02" size="20" @click="showPopup = false" /> 
        </div>
        <div class="flex-center b-margin-12">
            <van-button
                type="primary"
                class="width-90 border-radius-8"
                style="background: linear-gradient(99.4deg, #3C74EB 2.86%, #95D5F4 101.71%);border: none;"
                @click="confirm"
            >{{ '确定' }}
            </van-button>
        </div>
    </van-popup>
</template>

<script lang='ts' setup>
import { ref, onMounted, onBeforeMount, getCurrentInstance,watch } from 'vue'
import { useRoute } from 'vue-router'
import type { IOrderUsageRecordParams } from '@/types/order'
import orderService from '@/service/orderService' 
import type { IOrderUsageRecordResponseItem } from '@/types/order'
import noDataImg from '@/assets/images/no-data.png'
import Icon from '@/components/common/Icon.vue'
import type { PickerOption, DatePickerColumnType } from 'vant'
import { showToast } from 'vant'

const serviceName = (key: string) => {
    if(key === 'xs'){
        return '线索联系方式'
    }
    else if(key === 'swbg'){
        return '企业财税经营分析报告'
    }
    else if(key === 'gqbg'){
        return '高新技术科技企业报告'
    }
    else if(key === 'fpbg'){
        return '企业发展数据综合报告分析'
    }else if(key === 'znwh'){
        return '智能外呼'
    }else{
        return '-'
    }
}
const serviceType = (type:number) => {
    switch(type){
    case 2:return '已冻结'
    case 0:return '已退回'
    case 1:return '已使用'
    default:return ''
    }
}
const instance = getCurrentInstance()
const moment = instance?.appContext.config.globalProperties.$moment
const route = useRoute()
const Loading = ref<boolean>(true)
const listLoading = ref<boolean>(false)
const finished = ref<boolean>(false)

const list = ref<IOrderUsageRecordResponseItem[]>([])

const queryParams = ref<IOrderUsageRecordParams>({
    page: 1,
    pageSize: 100,
    transId: '',
})

const listOnload = () => {
    setTimeout(async () => {
        const res = await search(queryParams.value)
        // console.log(res)
        queryParams.value.page += 1
        listLoading.value = false
        if(list.value.length === res.total){
            finished.value = true
        }
    })
}
// 定义分组数据的接口
interface GroupedData {[key: string]: {
    id: number
    created_at: string
    number: number
    service_name:string
    isExpanded: boolean
    total: number
    extraInfo: Array<{
        id: number
        created_at: string
        change_type: number
        companyName: string
        belong: number
    }>;
  };
}
// 新增recordList变量
const recordList = ref<Array<{
    id: number
    created_at: string
    number: number
    service_name:string
    isExpanded: boolean
    total: number
    extraInfo: Array<{
        id: number
        change_type: number
        companyName: string
        belong: number
    }>
}>>([])

const search = async (params:IOrderUsageRecordParams) => {
    queryParams.value = params
    const res = await orderService.orderServiceUsagePage(queryParams.value)
    list.value.push(...res.data)
    
    // 按日期分组处理数据
    const groupedByDate: GroupedData = {}
    
    list.value.forEach(item => {
        const date = item.usageTime ? moment(item.usageTime).format('YYYY-MM') : '-'
        
        if (!groupedByDate[date]) {
            groupedByDate[date] = {
                id:1,
                created_at: item.usageTime ? moment(item.usageTime).format('YYYY-MM-DD') : '-',
                number: 0,
                service_name: '',
                isExpanded: true,
                total: 0,
                extraInfo: [],
            }
        }
        groupedByDate[date].id += 1
        groupedByDate[date].service_name = serviceName(item.serviceKey) || ''
        groupedByDate[date].number += (item.consumeAmount || 0)
        groupedByDate[date].total = groupedByDate[date].extraInfo.length
        groupedByDate[date].extraInfo.push({
            id: item.id,
            created_at: item.usageTime || '',
            change_type: item.usageType,
            companyName: item.companyName || '',
            belong: item.consumeAmount || 0
        })
    })
    
    // 转换为数组形式
    recordList.value = Object.values(groupedByDate)
    console.log('recordList',recordList.value)
    return res
}

const showPopup = ref<boolean>(false)
const active = ref(0)
// 月份选择相关
const now = new Date()
const currentYear = now.getFullYear()
const currentMonth = String(now.getMonth() + 1).padStart(2, '0')
const currentDay = String(now.getDate()).padStart(2, '0')
const currentDate_month = ref([String(currentYear), currentMonth])
const columnsType : DatePickerColumnType[] = ['year', 'month']
const formatter = (type:string, option:PickerOption) => {
    return option
}
const minDate = new Date(2020, 0, 1)
const maxDate = new Date(2025, parseInt(currentMonth, 10)-1, parseInt(currentDay, 10))

// 自定义时间相关
const selectedStatus = ref('')
const currentDate_customize = ref([String(currentYear), currentMonth,currentDay])
const onClickStatus = (status:string) => {
    selectedStatus.value = status
    switch (status) {
    case 'three-month':{
        const threeMonthsAgo = new Date(currentYear, parseInt(currentMonth, 10) - 1 - 3, parseInt(currentDay, 10))
        // console.log('threeMonthsAgo',threeMonthsAgo)
        startTime.value = `${threeMonthsAgo.getFullYear()}-${String(threeMonthsAgo.getMonth() + 1).padStart(2, '0')}-${String(threeMonthsAgo.getDate()).padStart(2, '0')}`
        break
    }
    case 'half-year':{
        const halfYearAgo = new Date(currentYear, parseInt(currentMonth, 10) - 1 - 6, parseInt(currentDay, 10))
        startTime.value = `${halfYearAgo.getFullYear()}-${String(halfYearAgo.getMonth() + 1).padStart(2, '0')}-${String(halfYearAgo.getDate()).padStart(2, '0')}`
        break
    }
    case 'one-year':{
        const oneYearAgo = new Date(currentYear, parseInt(currentMonth, 10) - 1 - 12, parseInt(currentDay, 10))
        startTime.value = `${oneYearAgo.getFullYear()}-${String(oneYearAgo.getMonth() + 1).padStart(2, '0')}-${String(oneYearAgo.getDate()).padStart(2, '0')}`
        break
    }
    default:
        break
    }
    endTime.value = `${currentYear}-${currentMonth}-${currentDay}`
}
const clearTime = () => {
    startTime.value = ''
    endTime.value = ''
    selectedStatus.value = ''
    activeDate.value = ''
}
const activeDate = ref('')
const onClickDate = (status:string) => {
    activeDate.value = status
    if (status === 'startTime') {
        if(startTime.value){
            currentDate_customize.value = startTime.value.split('-')
        }else{
            startTime.value = `${currentDate_customize.value[0]}-${currentDate_customize.value[1]}-${currentDate_customize.value[2]}`
        }
    }
    if ( status === 'endTime'){
        if(endTime.value){
            currentDate_customize.value = endTime.value.split('-')
        }else{
            endTime.value = `${currentDate_customize.value[0]}-${currentDate_customize.value[1]}-${currentDate_customize.value[2]}`
        }
    }
}
const startTime = ref('')
const endTime = ref('')

watch(() => startTime.value, () => {
    if(activeDate.value === 'startTime'){
        currentDate_customize.value = startTime.value.split('-')
    }
})
watch(() => endTime.value, () => {
    if(activeDate.value === 'endTime'){
        currentDate_customize.value = endTime.value.split('-')
    }
})

watch(() => currentDate_customize.value, () => {
    console.log('currentDate_customize.value',currentDate_customize.value)
    if(activeDate.value === 'startTime'){
        startTime.value = `${currentDate_customize.value[0]}-${currentDate_customize.value[1]}-${currentDate_customize.value[2]}`
    }
    else if(activeDate.value === 'endTime'){
        endTime.value = `${currentDate_customize.value[0]}-${currentDate_customize.value[1]}-${currentDate_customize.value[2]}`
    }
})

const ti = ref(0)
watch(showPopup, () => {
    ti.value = ti.value + 1
})

const confirm = () => {
    // console.log('confirm',active.value)
    if (active.value === 0) {
        console.log('currentDate_month.value',currentDate_month.value)
        const [year, month] = currentDate_month.value
        const startDate = moment(`${year}-${month}-01`).toDate()
        const endDate = moment(`${year}-${month}-01`).endOf('month').toDate()
        const params : IOrderUsageRecordParams = {
            ...queryParams.value,
            page:1,
            usageTimeRange: [startDate.getTime(), endDate.getTime()]
        }
        queryParams.value = params
    }
    if (active.value === 1) {
        if(!startTime.value){
            showToast({
                message: '请选择开始时间',
                type: 'fail'
            })
            return
        }else if(!endTime.value){
            showToast({
                message: '请选择结束时间',
                type: 'fail'
            })
            return
        }
        // console.log('startTime.value',startTime.value,'startTime.value',endTime.value)
        const startTimeStr = new Date(startTime.value)
        startTimeStr.setHours(0,0,0,0)
        const endTimeStr = new Date(endTime.value)
        endTimeStr.setHours(23,59,59,999)
        const startTimeTimestamp = new Date(startTimeStr).getTime()
        const endTimeTimestamp = new Date(endTimeStr).getTime()
        // console.log('startTimeTimestamp', startTimeTimestamp, 'endTimeTimestamp', endTimeTimestamp)
        if(startTimeTimestamp > endTimeTimestamp){
            showToast({
                message: '开始时间不可大于结束时间',
                type: 'fail'
            })
            return
        }
        const params : IOrderUsageRecordParams = {
            ...queryParams.value,
            page:1,
            usageTimeRange: [String(startTimeTimestamp), String(endTimeTimestamp)]
        }
        queryParams.value = params
    }
    console.log('queryParams.value',queryParams.value)
    list.value = []
    listOnload()
    showPopup.value = false
}

onBeforeMount(() => {
    queryParams.value.transId = route.query.transId as string
})
onMounted( () => {
    // console.log('onMounted',currentDay)
})
</script>

<style lang="scss" scoped>

:deep(.van-cell-group){
    margin-bottom:8px;
    border-radius: 8px;
    background-color: #F6FBFE;
}

:deep(.van-list-detail){
    width: 100%;
    .van-cell-detail{
        border-radius: 8px;
    }
}

:deep(.van-tabs__nav--line){
    width: 50%;
}

.status-btn {
    width: 2rem;
    height: 0.8rem;
    border: 1px solid #e5e5e5;
    background-color: #ffffff;
    border-radius: 4px;
    font-size: 14px;

    &.active {
        border-color: #3c74eb;
        background-color: #e5edff;
        color: #3c74eb;
    }
}

.date-range{
    font-size: 20px;
    width: 40%;
    height: 1rem;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;

    &.active{
        color: #3C74EB;
    }
}

.bottom-line {
    position: absolute; 
    bottom: 0; 
    width: 100%;
    border-bottom: 2px solid; 
}
</style>
