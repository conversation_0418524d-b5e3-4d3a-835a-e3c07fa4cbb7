<template>
    <div class="home all-padding-12 border-box" style="background-color: #f4fbff">
        <!-- 线索相关列表以及风险监控 -->
        <div class="display-flex flex-column gap-8">
            <div class="display-flex space-between all-padding-16 border-box border-radius-8" style="background-color: #ffffff;">
                <LeadMenus />
            </div>
            <div class="display-flex flex-column all-padding-16 border-box border-radius-8" style="background-color: #ffffff;">
                <span class="font-20 color-black font-weight-600 b-margin-24">我的代办</span>
                <MyAgent />
            </div>
            <div class="display-flex flex-column all-padding-16 border-box border-radius-8" style="background-color: #ffffff;">
                <span class="font-20 color-black font-weight-600 b-margin-24">企业风险监控</span>
                <companyRiskMonitor />
            </div>
            <div class="all-padding-24 border-box border-radius-8" style="background-color: #ffffff;">
                <span class="font-20 color-black font-weight-600 b-margin-24">销售漏斗</span>
                <div class="w-100" style="height: 5rem;margin-top: 2rem;">
                    <qiun-data-charts type="funnel" :opts="salesFunnelChartsOpt" :chartData="salesFunnelData" />
                </div>
            </div>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref, onMounted, computed } from 'vue'
import LeadMenus from './components/LeadMenus.vue'
import MyAgent from './components/MyAgent.vue'
import companyRiskMonitor from './components/companyRiskMonitor.vue'

const salesFunnelChartsOpt = ref({
    color: ['#1966FF', '#60BCFF', '#18B4AB'],
    padding: [15, 15, 0, 15],
    dataLabel: false,
    extra: {
        funnel: {
            type: 'triangle',
            activeOpacity: 0.3,
            activeWidth: 10,
            border: false,
            borderWidth: 2,
            borderColor: '#FFFFFF',
            fillOpacity: 1,
            labelAlign: 'right',
            linearType: 'none',
            minSize: 20
        }
    }
})

const salesFunnelData = computed(() => {
    return {
        series: [
            {
                data: [
                    {
                        name: '客户',
                        value: '123',
                        centerText: '123',
                    },
                        name: '线索',
                        value: '456',
                        centerText: '456',
                    },
                        name: '线索',
                        value: todoList.value[4]?.value || '0',
                        centerText: todoList.value[4]?.value || '0',
                    },
                    {
                        name: '成交客户',
                        value: todoList.value[6]?.value || '0',
                        centerText: todoList.value[6]?.value || '0',
                    }
                ]
            }
        ]
    }
})

const init = () => {}

onMounted(() => {
    init()
})

</script>

<style lang='scss' scoped>
.home {
    height: 100%;
    overflow: scroll;
    display: flex;
    flex-direction: column;
}
</style>