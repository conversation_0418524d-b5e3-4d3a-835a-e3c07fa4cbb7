<template>
    <div v-if="!loading" class=" lr-padding-16 tb-padding-12 back-color-common-grey">
        <van-empty v-if="!contactList.length" :image="noDataImg" image-size="224"
                   description="暂无联系方式" />
        <van-cell-group v-for="(contact, idx) in contactList" :key="idx" v-show="contactList.length"
                        class="b-margin-10">
            <van-cell>
                <template #title>
                    <div class="display-flex top-bottom-center gap-6">
                        <div class="contact-icon display-flex center" style="background-color:#D8E3FB;">
                            <icon v-if="!contact.contact" icon="icon-user-profile-02" :size="24" color="#3C74EB" />
                            <span class="color-blue font-16" v-else>{{ contact.contact.slice(0, 1) }}</span>

                        </div>
                        <div>
                            <div>{{ contact.content }} <van-tag v-if="contact.tagType === 1 || contact.tagType === 2"
                                                                :type="contact.tagType === 1 ? 'success' : 'warning'">{{ contact.tagType === 1 ?
                                                                    '推荐' : '关键' }}</van-tag> </div>
                            <div>来自：{{ contact.firstSourceName || '-' }}</div>

                        </div>

                    </div>
                </template>
                <template #right-icon>
                    <div class="display-flex top-bottom-center gap-12">
                        <icon icon="icon-phone-call-01" v-if="contact.type === 1 || contact.type === 2" :size="16"
                              color="" @click="makePhone(contact)" />
                        <icon icon="icon-copy-right" :size="16" color="" @click="copyContact(contact)" />

                    </div>
                </template>
            </van-cell>
        </van-cell-group>
        <van-divider v-if="contactList.length">已经到底啦</van-divider>
        
    </div>

    <van-skeleton v-else title :row="10" />


</template>

<script lang='ts' setup>
import { showToast, showDialog } from 'vant'
import { ref, onMounted, computed, reactive } from 'vue'
import type { Ref } from 'vue'
import { useRoute, useRouter } from 'vue-router'

import type { ContactItem, GetCompanyDataParams } from '@/types/company'
import noDataImg from '@/assets/images/no-data.png'
import aicService from '@/service/aicService'
const router = useRouter()
const route = useRoute()
const isPreset = computed(() => {
    const { query } = route
    const { preset } = query || {}
    return preset ? true : false
})
const socialCreditCode: Ref<string> = ref('')
const companyName: Ref<string> = ref('')

const contactList: Ref<ContactItem[]> = ref([])

const loading = ref<boolean>(false)
const getContactNum = () => {
    loading.value = true
    const queryParams = reactive<GetCompanyDataParams>(
        {
            socialCreditCode: socialCreditCode.value
        }
    )
    if (isPreset.value) {
        queryParams.preset = 1
    }
    aicService.gsGetContacts(queryParams).then((res) => {
        console.log(res)
        contactList.value = res.contacts

    }).finally(() => {
        loading.value = false
    })

}

const makePhone = (contact: ContactItem) => {
    let link = document.createElement('a')
    link.href = `tel:${contact.content}`
    link.click()
}

const copyContact = (contact: ContactItem) => {
    navigator.clipboard.writeText(contact.content).then(function () {
        showToast('复制成功')
    }).catch(function (err) {
        showToast(`无法复制文本:${err}`)
        console.error()
    })
}

onMounted(() => {
    if (route.query.socialCreditCode) {
        socialCreditCode.value = route.query.socialCreditCode as string
        companyName.value = route.query.companyName as string
        getContactNum()
    } else {
        showDialog({
            title: '提示',
            message: '缺少税号',
        }).then(() => {
            router.back()
        })

    }

})

</script>

<style lang='scss' scoped>
.contact-icon {
    width: 1rem;
    height: 1rem;
    border-radius: 0.5rem;
}

:deep(.van-cell-group){
    background:transparent !important;
}
</style>