import http from '@/axios'
import type { ICommonResponse } from '@/types/axios'

import type {
    IServiceOrderPageParams,
    IServiceOrderResponse,
    IOrderServiceStatisticsParams,
    IOrderServiceStatisticsResItem,
    IOrderBuyLegalResponse,
    IOrderParams,
    IOrderCheckEntBuyResponse,
    IOrderCheckEntBuyParams,
    IOrderInviteRecordParams,
    IOrderPaymentListItemPage,
    IOrderPaymentListRequest,
    IOrderPaymentCancel,
    IOrderGoodsDetailRequest,
    IOrderGoodsDetailResponse,
    IOrderGoodsOpenidRequest,
    IOrderGoodsOpenidResponse,
    IOrderPrepRequest,
    IOrderPrepResponse,
    IOrderServicePageResponse,
    IOrderGoodsServiceGoodsPageRequest,
    IOrderCheckEntBuyResponseArr,
    IOrderCheckUserPointsResponse,
    IOrderInvitedSummaryResponse,
    IOrderGoodsList,
    IOrderInviteRecordList,
    IOrderUsageRecordParams,
    IOrderUsageRecordResponse,
    IOrderRePay,
    IOrderPredeductRequest,
    IOrderPredeductResponse,
    IOrderGetServiceItemInfoResponse,
    IOrderGoodsValidateCouponRequest,
    IOrderGoodsValidateCouponResponse
} from '@/types/order'

export default {
    orderServiceStatistics(data: IOrderServiceStatisticsParams): Promise<IOrderServiceStatisticsResItem[]> {
        return http.get(`/api/zhenqi-order/order/service-statistics`, {
            params: data,
        })
    },
    orderServiceOrderPage(data: IServiceOrderPageParams): Promise<IServiceOrderResponse> {
        return http.get(`/api/zhenqi-order/order/service-order-page`, {
            params: data,
            hideError: true,
        })
    },
    // 查询使用记录
    orderServiceUsagePage(data: IOrderUsageRecordParams): Promise<IOrderUsageRecordResponse> {
        return http.get(`/api/zhenqi-order/order/service-usage-page`, {
            params: data,
            hideError: true,
        })
    },
    orderBuyLegal(data: IOrderParams): Promise<IOrderBuyLegalResponse> {
        return http.post(`/api/zhenqi-order/order/buy-legal`, data)
    },
    orderCheckEntBuy(
        data: IOrderParams | IOrderCheckEntBuyParams
    ): Promise<IOrderCheckEntBuyResponse | IOrderCheckEntBuyResponseArr> {
        return http.get(`/api/zhenqi-order/order/check-ent-buy`, {
            params: data,
        })
    },
    orderInviteNewInviteRecord(data: IOrderInviteRecordParams): Promise<IOrderInviteRecordList> {
        return http.post(`/api/zhenqi-order/invite-new/invite-record`, data, {
            hideError: true,
        })
    },
    goodsOrderList(data: IOrderPaymentListRequest): Promise<IOrderPaymentListItemPage> {
        return http.get(`/api/zhenqi-order/goods/order-list`, {
            params: data,
            hideError: true,
        })
    },
    goodsOrderCancel(data: IOrderPaymentCancel): Promise<ICommonResponse> {
        return http.put(`/api/zhenqi-order/goods/order-cancel`, data, {
            hideError: true,
        })
    },
    goodsOrderDetail(data: IOrderGoodsDetailRequest): Promise<IOrderGoodsDetailResponse> {
        return http.get(`/api/zhenqi-order/goods/order-detail`, {
            params: data,
            hideError: true,
        })
    },
    goodsGetOpenid(data: IOrderGoodsOpenidRequest): Promise<IOrderGoodsOpenidResponse> {
        return http.get(`/api/zhenqi-order/goods/get-openid`, {
            params: data,
            hideError: true,
        })
    },
    goodsOrderPrep(data: IOrderPrepRequest): Promise<IOrderPrepResponse> {
        return http.post(`/api/zhenqi-order/goods/order-prepay`, data, {
            hideError: true,
        })
    },
    goodsServiceGoodsPage(data: IOrderGoodsServiceGoodsPageRequest): Promise<IOrderServicePageResponse> {
        return http.get(`/api/zhenqi-order/goods/service-goods-page`, {
            params: data,
            hideError: true,
        })
    },
    // 查询用户积分信息
    orderInvitePoints(): Promise<IOrderCheckUserPointsResponse> {
        return http.get('/api/zhenqi-order/invite-new/invite-points')
    },
    // 查询邀请人数
    orderInvitedSummary(data?: { type: number }): Promise<IOrderInvitedSummaryResponse> {
        return http.get('/api/zhenqi-order/invite-new/invited-summary', {
            params: data,
        })
    },
    // 查询可兑换商品
    orderExchangeGoods(): Promise<IOrderGoodsList> {
        return http.get('/api/zhenqi-order/invite-new/exchange-goods', {
            hideError: true,
        })
    },
    // 积分兑换
    orderExchange(data: { goodsId: string }): Promise<ICommonResponse> {
        return http.post(`/api/zhenqi-order/invite-new/exchange?goodsId=${data.goodsId}`, data, {
            hideError: true,
        })
    },
    goodsOrderRepay(data: IOrderRePay): Promise<IOrderPrepResponse> {
        return http.put(`/api/zhenqi-order/goods/order-repay`, data, {
            hideError: true,
        })
    },
    // 权益预下单
    orderPrededuct(data: IOrderPredeductRequest): Promise<IOrderPredeductResponse> {
        return http.post(`/api/zhenqi-order/order/legal-pre-send`, data, {
            hideError: true,
        })
    },
    // 权益预下单确认
    orderConfirm(data: IOrderPredeductRequest): Promise<ICommonResponse> {
        return http.post(`/api/zhenqi-order/order/legal-confirm-send`, data, {
            hideError: true,
        })
    },
    // 查询订单相关信息
    orderGetServiceItemInfo(data: {id:string}): Promise<IOrderGetServiceItemInfoResponse> {
        return http.get('/api/zhenqi-order/order/legal-detail', {
            params: data,
            hideError: true,
        })
    },
    // 优惠券效验
    orderGoodsValidateCoupon(data: IOrderGoodsValidateCouponRequest): Promise<IOrderGoodsValidateCouponResponse>{
        return http.post('/api/zhenqi-order/goods/validate-coupon', data, {
            hideError: true
        })
    },
}
