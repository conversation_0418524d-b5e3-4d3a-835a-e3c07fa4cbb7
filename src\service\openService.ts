import http from '@/axios'
import { AxiosHeaders } from 'axios'

import type { ISsoAuthenticationParams, ISsoAuthenticationResponse } from '@/types/open.ts'

export default {
    ssoAuthentication(data: ISsoAuthenticationParams): Promise<ISsoAuthenticationResponse> { 
        return http.post(`/api/zhenqi-openapi/sso/authentication`, data,{
            headers: new AxiosHeaders({
                from: 'Y',
            }),
        })
    }
}