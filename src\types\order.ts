import type { IAllRecord } from '@/types/record'
import type { ContactItem } from '@/types/company'
import type { ICommonResponse, IPaginationResponse } from './axios'

export interface IServiceOrderPageParams extends IAllRecord {
    endCreateTime?: number
    endEndTime?: number
    serviceKey?: string
    startCreateTime?: number
    startEndTime?: number
    status?:string
    tenantId?: string
    sortBy?: string
    page: number
    pageSize: number
}

export interface IServiceOrderResponseItemService {
    app_id: string
    created_at: string
    id: number
    service_desc: string
    service_id: number
    service_name: string
    service_type: number
    should_masking: number
    status: number
    unit: string
    unit_quantity: number
    unit_type: number
    updated_at: string
}
export interface IServiceOrderResponseItem {
    transId: string
    id: string
    createTime: number,
    endTime: number,
    goodsId: string,
    remainingAmount: number,
    serviceKey: string,
    startTime: number,
    status: string,
    tenantId: string,
    tenantInfo: {
        name: string
    },
    totalAmount: number
}
export interface IServiceOrderResponse {
    data: IServiceOrderResponseItem[]
    errCode: number
    firstPage: boolean
    hasNextPage: boolean
    hasPreviousPage: boolean
    lastPage: boolean
    page: number
    pageSize: number
    success: boolean
    total: number
    totalPages: number
}
export interface IOrderServiceStatisticsParams extends IAllRecord {
    serviceKeys: string
    tenantId?: string
}
export interface IOrderServiceStatisticsResItem {
    label: string;
    num: number; 
}
export interface IOrderBuyLegalResponse extends ICommonResponse {
    contacts: ContactItem[]
    contactNum: number
}

export interface IOrderParams extends IAllRecord {
    serviceKey: string
    socialCreditCode: string
    companyName: string
    orderId?: number
}

export interface IOrderCheckEntBuyResponse {
    status: string
    totalBalance: string
    optionId: string
}

export interface IOrderCheckEntBuyResponseArr {
    data: [
        {
            optionId: string
            status: string
            expireTime: string
        },
    ]
}

export interface IOrderCheckEntBuyParams extends IAllRecord {
    socialCreditCode: string
}

export interface IOrderInviteRecordParams {
    page: number
    pageSize: number
    userId?: string
    tenantId?: string
    type?: number
}

export interface IOrderBuyLegalResponse {
    data: []
    errCode: number
    firstPage: boolean
    hasNextPage: boolean
    hasPreviousPage: boolean
    lastPage: boolean
    page: number
    pageSize: number
    success: boolean
    total: number
    totalPages: number
}
export interface IOrderPaymentListRequest extends IAllRecord {
    page: number
    pageSize: number
    queryService?: string
    orderStatus: string
}

export interface IOrderPaymentListItemPage extends IPaginationResponse {
    data: IOrderPaymentListItem[]
}

export interface IOrderPaymentListItem {
    id: number
    out_order_id: string
    out_trade_no: string
    transaction_id: string
    quantity: number
    order_name: string
    mch_id: string
    payment_way: string
    payment_type: string
    order_status: string
    goods_id: number
    snapshot_id: number
    actual_amount: number
    credit_amount: number
    bill_amount: number
    total_bill_amount: number
    discount_amount: number
    extra_discount_percent: string
    extra_discount_amount: number
    total_discount_amount: number
    invoice_amount: number
    app_id: string
    extra_info: string
    delay_time: string
    time_end: string
    notify_url: string
    user_id: string
    channel_id: 1
    order_for: 2
    notice_url: string
    created_at: string
    updated_at: string
    order_status_str: string
    payment_way_str: string
    service_order: []
}

export interface IOrderPaymentCancel {
    orderId: string
}

export interface IOrderGoodsDetailRequest extends IAllRecord {
    orderId: string
}

export interface IOrderGoodsDetailResponse extends ICommonResponse {
    data: IOrderPaymentListItem
}

export interface IOrderGoodsOpenidRequest extends IAllRecord {
    code: string
}

export interface IOrderGoodsOpenidResponse extends ICommonResponse {
    data: {
        openid: string
    }
}

export interface IOrderPrepRequest {
    goodsId: string
    openid: string
    quantity: number
    couponCode?: string
}

export interface IOrderPrepResponse extends ICommonResponse {
    data: IOrderPrepPayparams
}

export interface IOrderPrepPayparams extends ICommonResponse {
    appId: string
    timeStamp: string
    nonceStr: string
    package: string
    signType: string
    paySign: string
    order_id: string
    partnerid: string
    order_status: string
    out_order_id?: string
}

export interface IOrderGoodsServiceGoodsPageRequest extends IAllRecord {
    page: number
    pageSize: number
    serviceKey: string
}

export interface IOrderServicePageItem {
    id: number
    category_id: number
    goods_id: 'string'
    priority: number
    created_at: 'string'
    updated_at: 'string'
    goods: {
        id: number
        goods_name: 'string'
        goods_desc: 'string'
        goods_amount: number
        goods_discount: number
        goods_expire_time: 'string'
        is_allow_multiple: number
        is_allow_extend: number
        is_allow_negative: number
        goods_type: number
        goods_status: number
        purchase_rule: 'string'
        created_at: 'string'
        updated_at: 'string'
    }
    category: {
        id: number
        category_name: 'string'
        app_id: 'string'
        created_at: 'string'
        updated_at: 'string'
    }
    service: [
        {
            id: number
            goods_id: 'string'
            service_id: number
            quantity: 'string'
            unit: 'string'
            unit_type: number
            exp_quantity: number
            amount: number
            updated_at: 'string'
            created_at: 'string'
            service_item: {
                id: number
                service_id: number
                service_name: 'string'
                service_desc: 'string'
                service_type: number
                app_id: 'string'
                unit: 'string'
                unit_type: number
                unit_quantity: number
                should_masking: number
                status: number
                created_at: 'string'
                updated_at: 'string'
            }
        },
    ]
}

export interface IOrderServicePageResponse extends IPaginationResponse {
    data: IOrderServicePageItem[]
}

export interface IOrderCheckUserPointsResponse extends ICommonResponse {
    id: string
    tenantId: string
    orgId: string
    balance: number
}

export interface IOrderInvitedSummaryResponse extends ICommonResponse {
    number: number
    totalPoints: number
}

export interface IOrderGoodsListItem {
    id: string
    name: string
    points: number
    goodsId: string
}
export interface IOrderGoodsList extends ICommonResponse {
    data: IOrderGoodsListItem[]
}

interface goodsItem {
    id: string
    name: string
    points: number
    goodsId: string
}
export interface IOrderInviteRecordListItem {
    accountId: string
    amount: number
    createTime: number
    id: string
    type: number
    relationUser?: string
    relationUserName?: string
    relationGoods?: string
    msg?: string
    goods?: goodsItem[]
    relationUserMobile?: string
}

export interface IOrderInviteRecordList extends IPaginationResponse {
    data: IOrderInviteRecordListItem[]
}

export interface IOrderUsageRecordParams extends IAllRecord {
    page: number
    pageSize: number
    transId: string
    usageTimeRange?: string[]
}

export interface IOrderUsageRecordResponseItem {
    id:number
    companyName: string
    consumeAmount: number
    operatorName: string
    serviceKey: string
    socialCreditCode: string
    tenantName: string
    usageTime: string
    usageType: number
}

export interface IOrderUsageRecordResponse {
    data: IOrderUsageRecordResponseItem[]
    errCode: number
    firstPage: boolean
    hasNextPage: boolean
    hasPreviousPage: boolean
    lastPage: boolean
    page: number
    pageSize: number
    success: boolean
    total: number
    totalPages: number
}

export interface IOrderRePay {
    orderId: string
}
export interface IOrderPredeductRequest {
    transId: string
    prePayAmount?: number
    isGift?: number
}

export interface IOrderPredeductRequestItem {
    app_id: string
    balance: number
    channel_id: number
    created_at: string
    expire_time: string
    id: number
    is_allow_negative: number
    is_expired: boolean
    out_order_id: string
    quantity: number
    service: IServiceOrderResponseItemService
    service_id: number
    unit_type: number
    updated_at: string
    user_id: string
}
export interface IOrderPredeductResponse extends ICommonResponse {
    data:{
        id: string,
        uuid: string,
    }
}

export interface IService{
    appId: string
    createdAt: string
    id: number
    serviceDesc: string
    serviceId: number
    serviceName: string
    serviceType: number
    shouldMasking: number
    status: number
    unit: string
    unitQuantity: number
    unitType: number
    updatedAt: string
}

export interface IServiceItem{
    appId: string
    balance: number
    channelId: number
    createdAt: string
    expireTime: string
    id: number
    isAllowNegative: number
    isExpired: false
    outOrderId: string
    quantity: number
    serviceId: number
    specServiceId: number
    unitType: number
    updatedAt: string
    userId: string
    service: IService
    // totalAmount?: number
    // remainingAmount?: number
    // goodsId?: string
    // tenantInfo?: {
    //     name: string
    // }
    // tenantId?: string
    // startTime?: string
    // serviceKey?: string
    // endTime?: number
    // status?: string
}

export interface IOrderGetServiceItemInfo {
    createTime:number
    endTime:number
    goodsId: string
    id: string
    isPrePayOrder:number
    prePayAmount: number
    sendUserName: string
    sourceTransId:string
    startTime: number
    updateTime:number
    transId: string
    expireTime:number
    serviceKey:string
}
export interface IOrderGetServiceItemInfoResponse extends ICommonResponse {
    data: IOrderGetServiceItemInfo
}

export interface IOrderGoodsValidateCouponRequest {
    code: string
    amount: number
    goodsId: string
}

export interface IOrderGoodsValidateCouponResult{
    coupon_id: number
    coupon_code: string
    coupon_name: string
    original_amount: number
    discount_amount: number
    final_amount: number
    discount_type: string
    min_order_amount:number
}
export interface IOrderGoodsValidateCouponResponse{
    data: IOrderGoodsValidateCouponResult
    errCode: number
    errMsg: string
    success: boolean
}