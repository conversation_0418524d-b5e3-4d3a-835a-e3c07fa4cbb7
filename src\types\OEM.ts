import type { ICommonResponse } from './axios'
import type { IAllRecord } from './record'

export interface OEMDetailParams extends IAllRecord{
    key: string
    productType: 0 | 1 | 2 | 3
}

export interface JYHY { 
    mpLogo?:string //小程序首页logo
    mpHomeBanner?:string //小程序首页顶部图
    mpHomeSpec?:string //小程序首页搜索框下方图
    easyCollect?:boolean//是否开启精简授权
    pyr:boolean//是否开启票易融
    mpQrCode?:string//业务进件二维码海报图
    qrCodeImg?:string//业务进件分享图
    jinrongEduApplyQrcode?:string// 业务进件二维码信息  
}

interface OEMConfigModule {
    config: JYHY 
    productType: 0 | 1 | 2 | 3
    productTypeStr: string
}

export interface IOEMConfig {
    key: string
    modules: OEMConfigModule[]
}

export interface IOEMDetailResponse extends ICommonResponse {
    data: IOEMConfig
}