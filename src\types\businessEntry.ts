import type { ICommonResponse } from './axios'
import type { IAllRecord } from './record'

export interface ICrmJRApplyParams extends IAllRecord {
    companyName: string
    intendedAmount: number
    mobile: string
    name: string
    refCode: string
    socialCreditCode: string
    useOfFunds: string
    verificationCode: string
}

export interface ICrmJRApplyResponse extends ICommonResponse {
    data:ICrmJRApplyResponseItem
}

export interface ICrmJRApplyResponseItem {
    url:string
    resquestId:string
}