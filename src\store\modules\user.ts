import type { RootState, UserState } from '@/types/store'
import type { IUserAccountInfoResponse } from '@/types/user'
import { type Module } from 'vuex'

export const originalState: UserState = {
    account: null,
}

const userModule: Module<UserState, RootState> = {
    namespaced: true, // 启用命名空间
    state: () => originalState,
    getters: {
        account: (state: UserState) => state.account,
    },
    mutations: {
        SET_ACCOUNT(state: UserState, account: IUserAccountInfoResponse) {
            state.account = account
        },
    },
    actions: {
        setAccountInfo({ commit }, account: IUserAccountInfoResponse) {
            commit('SET_ACCOUNT', account)
        },
    },
}

export default userModule
