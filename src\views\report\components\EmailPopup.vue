<template>
    <van-popup v-model:show="sendEmailVisible" style="top: 40%; border-radius: 8px;" :close-on-click-overlay="false">
        <div class="w-291 back-color-white border-radius-8 lr-padding-16">
            <div class="font-16 font-weight-500 color-black tb-margin-12 text-center relative">报告下载
                <div class="absolute top-0 right-0" @click="handleCancel()">
                    <van-icon name="cross" />
                </div>
            </div>
            <van-form ref="emailForm" required="auto" @submit="confirmEmail">
                <van-field name="email" label-align="top" v-model="email" :rules="[
                    { required: true, message: '请输入邮箱地址' }, {
                        pattern: /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                        message: '请输入邮箱地址'
                    }
                ]" label="邮箱地址" placeholder="请输入邮箱地址" />
                <div class="t-margin-12 b-margin-12">
                    <van-button type="primary" class="width-100 h-36 border-radius-8"
                                style="background: linear-gradient(to right, #3c74eb, #95d5f4); border: none;"
                                native-type="submit">确认发送</van-button>
                </div>
            </van-form>
        </div>
    </van-popup>
</template>

<script lang='ts' setup>
import { ref } from 'vue'
import type { Ref } from 'vue'
import { showDialog } from 'vant'
import reportService from '@/service/reportService'
import type { ReportRecordItem, ISendEmailReportParams } from '@/types/report'

const item = ref<ReportRecordItem>()
const showSendEmailVisible = (val:ReportRecordItem) => {
    item.value = val
    sendEmailVisible.value = true
}
const sendEmailVisible: Ref<boolean> = ref(false)
const email: Ref<string> = ref('')
const emailForm = ref()
const handleCancel = () => {
    sendEmailVisible.value = false
    email.value = ''
}
const confirmEmail = (values: { email: string }) => {
    if (!item.value) return 
    const reportData = ref<ISendEmailReportParams>(
        {
            socialCreditCode: item.value.socialCreditCode ,
            reportId: item.value.requestId || '',
            taxRequestId: item.value.requestId || '',
            email: values.email,
            reportType: item.value.reportType.toLowerCase(),
            companyName: item.value.companyName || '',
        }
    )
    // 判断是不是示例数据
    const { reportUrl } = item.value || {}
    if (reportUrl && reportUrl.includes('preset=1')) {
        reportData.value.previewUrl = item.value.reportUrl
    }
    reportService.sendEmailReport(reportData.value).then((res) => {
        console.log('res', res)
        showDialog({
            message: '提交成功，5分钟内将发送至您的邮箱，请及时查收',
            confirmButtonText: '我知道了',
            messageAlign: 'left',
        }).then(() => { })
        sendEmailVisible.value = false
    })
}
defineExpose({
    showSendEmailVisible
})
</script>