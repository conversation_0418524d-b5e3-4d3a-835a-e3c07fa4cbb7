<script setup lang="ts">
import { computed, onBeforeMount } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { keepAliveRouteNames } from '@/router'
const route = useRoute()
const router = useRouter()

const title = computed(() => {
    const { meta } = route
    const { title } = meta || {}
    if (typeof title !== 'string') return ''

    return title || ''
})

const onClickLeft = () => {
    console.log(router)
    const previousRoute = router.options.history.state.back
    if (previousRoute) {
        router.go(-1)
    } else {
        router.push({ name: 'home' })
    }
}

onBeforeMount(() => {
    // userService.userGetAccountInfo().then((account) => {
    //     store.dispatch('user/setAccountInfo', { ...account })
    // })
})
</script>

<template>
    <div class="common-layout flex flex-column flex-1 height-100">
        <van-nav-bar :title="title" left-arrow @click-left="onClickLeft" />
        <router-view v-slot="{ Component }">
            <keep-alive :include="keepAliveRouteNames">
                <component :is="Component" />
            </keep-alive>
        </router-view>
    </div>
</template>

<style lang="scss" scoped>
.common-layout {
    --van-nav-bar-icon-color: var(--main-black);
}
</style>
