import type { IAllRecord } from '@/types/record'
export interface ITenantSearchOption {
    key: string
    type: 'input' | 'select' | 'multipleSelect' | 'date' | 'cascader' | 'slider' | 'checkAllMultipleSelect'
    placeholder: string
    label: string
    editable: boolean
    isShow: boolean
    options?: Array<{
        label: string
        value: number | string
    }>
    props?: {
        label?: string
        value?: string
        emitPath?: boolean
        checkStrictly?: boolean
    }
}

export interface ITenantPageParams extends IAllRecord {
    page: number
    pageSize: number
    contact?: string
    createUser?: string
    name?: string
    phone?: string
    type?: string
    username?: string
}

export interface ITenantPageItem{
    id: string
    name: string
    mergeCollect: boolean
    marketingCardType: string
    type: number
    contact: string
    management: string
    phone: string
    areaCode: string
    areaName: string
    channelId: string
    isAuth: boolean
    oemKey: string
    orgId: string
    parentId: string
    createUser: string
    status: number
    openAiPhone: boolean
    allowProjectIds: string[]
}

export interface ITenantPageResponse{
    errCode: number
    success: boolean
    page: number
    pageSize: number
    total: number
    totalPages?: number
    data: ITenantPageItem[]
    hasPreviousPage?: boolean
    hasNextPage?: boolean
    firstPage?: boolean
    lastPage?: boolean
}
export interface IAddTenantParams{
    username: string // 用户名
    password: string //用户密码
    areaCode?: string
    areaName?: string 
    contact?: string // 联系人
    createUserRole?: boolean
    id?: string
    menuIds?: string[]
    name: string //租户名称
    oemKey?: string
    parentId?: string
    phone?: string // 联系电话
    allowProjectIds?: string[] // 授权类目
    openAiPhone?: boolean
    website?: string // 网站域名
    appid?: string // appid
    privateKey?: string // 私钥(APPID接口秘钥)
    aiSeatsNum?: number // 人工坐席数量
}

export interface IEditTenantParams{
    id: string
    areaCode?: string
    areaName?: string
    contact?: string
    name?: string
    oemKey?: string
    openAiPhone?: boolean
    parentId?: string
    phone?: string
    allowProjectIds?: string[]
    applicationConfig?: { // 外呼应用配置
        aiSeatsNum: number // 人工坐席数量
        privateKey: string // 私钥
        tenantId: string // 租户
        website: string // 网站域名
    }
}

export interface ITenantDisableParams{
    ids: string[]
    status: 0 | 1 // 0 停用  1 启用
}
export interface ITenantDataSourceParams{
    tenantId: string
    areaCodes?: string[]
    type?: string
    name?: string
    channelType?: string
}
export interface ITenantListResponse {
    id: string
    name: string
}
export interface ITenantRequest extends IAllRecord {
    tenantId: string
}
export interface IGetAicChannelResponse {
    channelId?: string
    channelName?: string
    channelType?: string
}
export interface IGetTaxCollectResponse {
    areaCodes?: string
    channelId?: string
    type?: string
}