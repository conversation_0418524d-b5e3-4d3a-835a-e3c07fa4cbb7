<script lang="ts" setup>
import { useRoute, useRouter } from 'vue-router'
import { ComboGroupList, LeadsGroupList } from './components'
import { onBeforeMount, onMounted, ref } from 'vue'
import orderService from '@/service/orderService'
import type { IOrderPrepPayparams } from '@/types/order'
import { closeToast, showFailToast, showLoadingToast } from 'vant'
import { removeQueryParam } from '@/utils/format'
const route = useRoute()
const router = useRouter()
const goods = route.query.goods
const code = route.query.code
const openid = route.query.openid

const payResult = route.query.payResult
const orderId = route.query.orderId
const openidRef = ref('')

const getUrl = (appid: string, state: string, url: string) => {
    let oauthUrl = 'https://weixin.shuzutech.com/get-code-iIa39TfV.html'
    oauthUrl = oauthUrl + '?scope=snsapi_base'
    const appidParam = '&appid=' + appid
    const stateParam = '&state=' + state
    const redirectUriParam = '&redirect_uri=' + encodeURIComponent(url)
    const redirectUrl = oauthUrl + appidParam + stateParam + redirectUriParam

    return redirectUrl
}

const createOrder = ({ goodsId, quantity, couponCode }: { goodsId: string; quantity: number; couponCode?: string }) => {
    if (openidRef.value === '') {
        return getCode()
    }
    let queryParams=ref()
    if (couponCode) {
        queryParams.value = {
            goodsId: goodsId,
            openid: openidRef.value,
            quantity: quantity,
            couponCode: couponCode
        }
    } else {
        queryParams.value = {
            goodsId: goodsId,
            openid: openidRef.value,
            quantity: quantity,
        }
    }

    showLoadingToast({
        message: '即将打开支付页面...',
        forbidClick: true,
    })
    orderService
        .goodsOrderPrep(queryParams.value)
        .then((res) => {
            if (res.errCode === 0) {
                const { order_status, out_order_id } = res.data
                if (order_status && order_status === '1' && out_order_id) {
                    router.push({
                        name: 'myOrdersDetail',
                        query: {
                            orderId: out_order_id,
                        },
                    })
                } else {
                    console.log('createOrder 成功')
                    doPay(res.data)
                }
            } else {
                showFailToast({
                    message: res.errMsg || '无法打开支付页面，请稍后再试',
                    duration: 2000
                })
            }
        })
        .finally(() => {
            closeToast()
        })
}

const doPay = (data: IOrderPrepPayparams) => {
    const successUrl = encodeURIComponent(
        `${location.href}&payResult=success&orderId=${data.order_id}&openid=${openidRef.value}`
    )

    const failedUrl = encodeURIComponent(
        `${location.href}&payResult=failed&orderId=${data.order_id}&openid=${openidRef.value}`
    )

    let url = 'https://weixin.shuzutech.com/jsapi-pay.html?'
    url = url + `appId=${data.appId}`
    url = url + `&timeStamp=${data.timeStamp}`
    url = url + `&nonceStr=${data.nonceStr}`
    url = url + `&package=${data.package}`
    url = url + `&signType=${data.signType}`
    url = url + `&paySign=${data.paySign}`
    url = url + `&successUrl=${successUrl}`
    url = url + `&failedUrl=${failedUrl}`

    window.location.href = url
}

const getOpenId = () => {
    if (!code) return
    orderService
        .goodsGetOpenid({
            code: code.toString(),
        })
        .then((res) => {
            if (res.errCode === 0) {
                console.log('openid', res.data.openid)
                openidRef.value = res.data.openid
            } else if (res.errMsg.includes('invalid code') || res.errMsg.includes('code been used')) {
                return getCode()
            }
        })
}

const getCode = () => {
    const currentUrl = document.location.origin + document.location.pathname + '?' + removeQueryParam('code')
    const url = getUrl('wx70e22978a452adcb', 'STATE', currentUrl)
    window.location.href = url
}

onMounted(() => {
    if (openid) {
        openidRef.value = openid.toString()
        return
    }

    if (code) {
        return getOpenId()
    }
    getCode()
})

onBeforeMount(() => {
    if (!payResult) return
    if (payResult === 'success') {
        router.replace({
            name: 'myOrdersDetail',
            query: {
                orderId: orderId,
            },
        })
    }

    if (payResult === 'failed') {
        router.replace({
            name: 'myOrdersDetail',
            query: {
                orderId: orderId,
            },
        })
    }
})
</script>

<template>
    <div class="goods-list tb-padding-12 lr-padding-16 height-100">
        <LeadsGroupList v-if="goods === 'leads-group'" :create-order="createOrder" />
        <ComboGroupList v-if="goods === 'combo-group'" :create-order="createOrder" />
    </div>
</template>

<style lang="scss" scoped></style>
