//盒子模型相关css，如padding,margin,border,box-sizing,flex等

//width
@for $i from 10 through 100 {
    @if $i % 10==0 {
        .width-#{$i} {
            width: #{$i}+'%';
        }
    }
}

//height
@for $i from 10 through 100 {
    @if $i % 10==0 {
        .height-#{$i} {
            height: #{$i}+'%';
        }

        .height-#{i}vh {
            height: #{$i}+'vh';
        }
    }
}

//border

@for $i from 2 through 100 {
    @if $i % 2==0 {
        .border-radius-#{$i} {
            border-radius: #{$i}px;
        }
    }
}

//margin

@for $i from 2 through 100 {
    .b-margin-#{$i} {
        margin-bottom: #{$i}px;
    }

    .l-margin-#{$i} {
        margin-left: #{$i}px;
    }

    .r-margin-#{$i} {
        margin-right: #{$i}px;
    }

    .t-margin-#{$i} {
        margin-top: #{$i}px;
    }

    .all-margin-#{$i} {
        margin: #{$i}px;
    }

    .tb-margin-#{$i} {
        margin-top: #{$i}px;
        margin-bottom: #{$i}px;
    }

    .lr-margin-#{$i} {
        margin-left: #{$i}px;
        margin-right: #{$i}px;
    }
}

//padding

@for $i from 0 through 200 {
    .t-padding-#{$i} {
        padding-top: #{$i}px;
    }

    .b-padding-#{$i} {
        padding-bottom: #{$i}px;
    }

    .l-padding-#{$i} {
        padding-left: #{$i}px;
    }

    .r-padding-#{$i} {
        padding-right: #{$i}px;
    }

    .all-padding-#{$i} {
        padding: #{$i}px;
    }

    .tb-padding-#{$i} {
        padding-top: #{$i}px;
        padding-bottom: #{$i}px;
    }

    .lr-padding-#{$i} {
        padding-left: #{$i}px;
        padding-right: #{$i}px;
    }
}

//width px
@for $i from 0 through 1000 {
    .w-#{$i} {
        width: #{$i}+'px';
    }
}

//height px
@for $i from 0 through 1000 {
    .h-#{$i} {
        height: #{$i}+'px';
    }
}

// max-height
@for $i from 0 through 1000 {
    .max-height-#{$i} {
        max-height: #{$i}+'px';
    }
}

// max-width
@for $i from 10 through 960 {
    @if $i % 10==0 {
        .max-width-#{$i} {
            max-width: #{$i}+'%';
        }
    }
}

// height
@for $i from 10 through 100 {
    @if $i % 10==0 {
        .height-#{$i} {
            height: #{$i}+'%';
        }
    }
}

// min-width percentage
@for $i from 10 through 100 {
    @if $i % 10==0 {
        .min-width-#{$i} {
            min-width: #{$i}+'%';
        }
    }
}

// min-width px
@for $i from 10 through 1920 {
    @if $i % 10==0 {
        .mw-#{$i} {
            min-width: #{$i}+'px';
        }
    }
}

// max-width px
@for $i from 10 through 960 {
    @if $i % 10==0 {
        .maxw-#{$i} {
            max-width: #{$i}+'px';
        }
    }
}