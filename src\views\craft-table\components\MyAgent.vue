<template>
    <div class="display-flex space-between" >
        <div v-for="item in dataList" :key="item.id">
            <div class="flex-center flex-column gap-8">
                <div class="font-20 font-weight-600">{{ item.num }}</div>
                <div class="font-14">{{ item.label }}</div>
            </div>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { ref } from 'vue'

const dataList = ref([
    {
        id:1,
        num: 100000,
        label:'新增线索'
    },
    {
        id:2,
        num: 999,
        label:'新增客户'
    },
    {
        id:3,
        num: 10,
        label:'超7天未跟进'
    },
    {
        id:4,
        num: 30,
        label:'超30天未跟进'
    },
    
])

</script>

<style lang='scss' scoped>
</style>