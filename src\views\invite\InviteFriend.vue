<template>
    <div class="height-100 back-color-common lr-padding-16 oh display-flex flex-column space-between">
        <div class="h-552 t-margin-12 share-page" id="share-page">
            <div class="w-358 h-432 top-content relative lr-padding-20 border-box">
                <div class="t-padding-43 font-16 color-black">{{ userInfo?.nickname || '' }} 送您</div>
                <div class="font-24 font-weight-500 color-black">企业风险免费筛查</div>
                <div class="t-margin-8 font-12 color-two-grey">企业工商司法数据全面风险筛查</div>
                <div class="width-100  display-flex space-between font-12 absolute bottom-12">
                    <div>
                        <span class="color-black">{{ formattedDate }}</span>&nbsp;<span class="color-two-grey">{{ weekday }}</span>
                    </div>
                    <div class="r-padding-40 display-flex top-bottom-center"><img class="w-14 h-12" src="@/assets/images/zqy-logo.png" alt="臻企云服"> <div class="l-margin-4">臻企云服</div></div>
                </div>
            </div>
            <!-- 二维码 -->
            <div class="h-120 back-color-white display-flex space-between top-bottom-center lr-padding-20" style="border-bottom-right-radius: 8px; border-bottom-left-radius: 8px;">
                <div>
                    <div class="font-12 color-two-grey b-margin-6">我的邀请二维码</div>
                    <div class="font-15 color-black>">长按识别二维码领取权益</div>
                </div>  
                <div class="w-80 h-80" style="background: #3c74eb;" ref="qrcodeContainer">
                    <!-- 用于生成二维码 -->
                    <canvas ref="qrcodeCanvas" v-show="false"></canvas>
                    <!-- 用于显示&&微信浏览器识别 -->
                    <img ref="qrcodeImg" :src="qrCodeImageSrc" alt="QR Code" style="width: 100%; height: 100%">
                </div>
            </div>
        </div>
        <!-- 分享通道 -->
        <div class="b-margin-12">
            <van-button type="primary" class="h-46 width-100 border-radius-8" style="background: linear-gradient(to right, #3c74eb, #95d5f4); border: none;" @click=" isWechatBrowser ? saveToWechat() : savePageAsImage()">保存图片</van-button>
        </div>
    </div>
</template>
<script lang="ts" setup>
import { onMounted, ref, computed } from 'vue'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'

import html2canvas from 'html2canvas'
import QRCode from 'qrcode'

const store = useStore<RootState>()
const userInfo = computed(() => {
    const { account } = store.state.user
    const { user } = account || {}
    return user
})

const isWechatBrowser = computed(() => {
    const ua = navigator.userAgent.toLowerCase()
    return /micromessenger/i.test(ua)
})

const formattedDate = ref<string>('')
const weekday = ref<string>('')

const savePageAsImage = async () => {
    try {
        const canvas = await html2canvas(document.getElementById('share-page') as HTMLElement)
        const image = canvas.toDataURL('image/png')
        // 兼容 Safari 的方式
        const link = document.createElement('a')
        link.href = image
        link.download = 'screenshot.png'
        document.body.appendChild(link) // 必须添加到 DOM 中
        // 如果是 iOS，可能需要提示用户长按图片保存
        if (/iPad|iPhone|iPod/.test(navigator.userAgent)) {
            window.setTimeout(() => {
                link.click()
            },300)
        } else {
            link.click()
        }
        document.body.removeChild(link) // 完成后移除
    } catch (error) {
        console.error('保存失败:', error)
    }
}

// 1. 生成图片的函数
const generateImage = async () => {
    const element = document.getElementById('share-page') as HTMLElement
    return await html2canvas(element, {
        useCORS: true, // 是否跨域
        scale: 2, // 清晰度
        logging: false, // 日志
        backgroundColor: '#ffffff' // 背景
    })
}

// 2. 显示图片弹窗让用户长按保存
const showImagePopup = (canvas: HTMLCanvasElement) => {
    const imageUrl = canvas.toDataURL('image/png')
    
    // 创建全屏遮罩层
    const wrapper = document.createElement('div')
    wrapper.style.cssText = `
        position: fixed;
        top: 0;
        left: 0;
        right: 0;
        bottom: 0;
        background: rgba(0,0,0,0.9);
        display: flex;
        flex-direction: column;
        align-items: center;
        justify-content: center;
        z-index: 9999;
    `
    
    // 创建图片容器
    const img = new Image()
    img.src = imageUrl
    img.style.maxWidth = '90%'
    img.style.maxHeight = '80%'
    
    // 创建提示文字
    const tip = document.createElement('div')
    tip.textContent = '长按图片保存到手机相册'
    tip.style.color = 'white'
    tip.style.marginTop = '20px'
    tip.style.fontSize = '16px'
    
    // 组装元素
    wrapper.appendChild(img)
    wrapper.appendChild(tip)
    wrapper.onclick = () => document.body.removeChild(wrapper)
    
    document.body.appendChild(wrapper)
}

// 3. 主保存函数
const saveToWechat = async () => {
    try {
        const canvas = await generateImage()
        showImagePopup(canvas)
    } catch (error) {
        console.log('error', error)
        // 降级处理：提示用户截图
        alert('保存失败，请手动截图保存\n（同时按住电源键+音量下键）')
    }
}

// 生成二维码
const qrCodeUrl = `${window.location.origin}/receive-award?userId=${userInfo.value?.id}`
const qrcodeContainer = ref<HTMLDivElement | null>(null)
const qrcodeCanvas = ref<HTMLCanvasElement | null>(null)
const qrcodeImg = ref<HTMLImageElement | null>(null)
const qrCodeImageSrc = ref('') // 存储 base64 图片

const getDate = () => {
    const today = new Date()
    const year = today.getFullYear()
    const month = String(today.getMonth() + 1)
    const day = String(today.getDate()).padStart(2, '0')
    formattedDate.value = `${year}/${month}/${day}`
    const weekdayList = ['星期日', '星期一', '星期二', '星期三', '星期四', '星期五', '星期六']
    weekday.value = weekdayList[today.getDay()]
    
}
onMounted(() => {
    console.log('userInfo', userInfo.value)
    console.log('isWechatBrowser', isWechatBrowser.value)
    getDate()
    if (qrcodeCanvas.value && qrcodeContainer.value) {
        const containerWidth = qrcodeContainer.value.offsetWidth
        const containerHeight = qrcodeContainer.value.offsetHeight
        const qrSize = Math.min(containerWidth, containerHeight)
        console.log('qrCodeUrl',qrCodeUrl)
        
        // 1. 先在 canvas 上生成二维码
        QRCode.toCanvas(qrcodeCanvas.value, qrCodeUrl, {
            width: qrSize,
            height: qrSize,
            margin: 2, // 增加边距，提高识别率
        }, (error: Error | null) => {
            if (error) {
                console.error('生成二维码失败:', error)
                return
            }
            // 2. 把 canvas 转换成 base64 图片
            qrCodeImageSrc.value = qrcodeCanvas.value!.toDataURL('image/png')
        })
    }
})
</script>
<style scoped lang="scss">
.top-content{
    background-image: url('@/assets/images/invite-new-share-bg.png');
    background-size: 100% 100%;
    background-position: center;
    background-repeat: no-repeat;
}
</style>