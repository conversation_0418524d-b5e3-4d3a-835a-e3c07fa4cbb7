<template>
    <div v-if="isShowSuccess" class="flex-center">
        <img src="@/assets/images/business-entry-success.png" alt="" width="50%" >
    </div>
    <div v-if="!isShowSuccess" class="height-100 oh t-margin-12 display-flex flex-column font-16">
        <div class="flex-center" style="color: #000000;">票易融在线测额</div>
        <div class="t-margin-12 " style="border: 1px solid #f2f2f2;"></div>
        <div class="height-100 lr-padding-16 display-flex flex-column space-between">
            <van-form class="color-two-grey">
                <div class="t-margin-8">
                    <span class="color-red r-margin-4">*</span>
                    <span class="">姓名</span>
                </div>
                <van-field
                    class="field"
                    v-model="name"
                    placeholder="请填写姓名"
                    :rules="[{ required: true, message: '请填写姓名' }]"
                    clearable
                >
                </van-field>
                <div class="t-margin-8">
                    <span class="color-red r-margin-4">*</span>
                    <span class="color-two-grey">公司名称</span>
                </div>
                <van-field
                    class="field"
                    v-model="companyName"
                    placeholder="请填写公司名称"
                    :rules="[{ required: true, message: '请填写公司名称' }]"
                    @update:model-value="handleSearchKeyChange"
                    @click="showPopup = true"
                    readonly
                    clearable
                >
                </van-field>
                <van-popup position="bottom" v-model:show="showPopup" class="all-padding-12" style="height: 80%;">
                    <van-field
                        class="field"
                        v-model="searchKey"
                        placeholder="请输入关键字搜索"
                        @update:model-value="handleSearchKeyChange"
                    >
                    </van-field>
                    <van-picker
                        confirm-button-text=""
                        cancel-button-text=""
                        @cancel="showPopup = false"
                        :columns="companyList"
                        :loading="pickLoading"
                        @click-option="handleSelectCompany"
                    >
                        <template #empty>
                            <van-empty
                                :image="noData"
                                image-size="200"
                                description="暂无数据"
                            />
                        </template>
                    </van-picker>
                </van-popup>
                <div class="t-margin-8">
                    <span class="color-red r-margin-4">*</span>
                    <span class="color-two-grey">资金用途</span>
                </div>
                <van-field
                    class="field"
                    v-model="fundUse"
                    placeholder="请填写资金用途"
                    :rules="[{ required: true, message: '请填写资金用途' }]"
                    clearable
                >
                </van-field>
                <div class="t-margin-8">
                    <span class="color-red r-margin-4">*</span>
                    <span class="color-two-grey">意向申请额度</span>
                </div>
                <van-field
                    class="field"
                    v-model="applyAmount"
                    placeholder="请填写意向申请额度"
                    :rules="[{ required: true, message: '请填写意向申请额度' }]"
                    clearable
                >
                    <template #extra>
                        万元
                    </template>
                </van-field>
                <div class="t-margin-8">
                    <span class="color-red r-margin-4">*</span>
                    <span class="color-two-grey">手机号码</span>
                </div>
                <van-field
                    class="field"
                    v-model="phoneNumber"
                    placeholder="请填写手机号码"
                    :rules="[{ required: true, message: '请填写手机号码' }]"
                    clearable
                >
                </van-field>
                <div class="t-margin-8">
                    <span class="color-red r-margin-4">*</span>
                    <span class="color-two-grey">验证码</span>
                </div>
                <van-field
                    class="field"
                    v-model="verifyCode"
                    placeholder="请填写验证码"
                    :rules="[{ required: true, message: '请填写验证码' }]"
                    clearable
                >
                    <template #button>
                        <van-button
                            size="large"
                            type="primary"
                            class="smsBtn"
                            :disabled="countdown > 0"
                            @click="getCode"
                            :loading="querying"
                        >
                            {{ countdown > 0 ? `${countdown} 秒` : '获取验证码' }}</van-button
                        >
                    </template>
                </van-field>
            </van-form>
            <div class="b-margin-36">
                <van-button
                    :loading="buttonLoading"
                    type="primary" 
                    class="h-46 width-100 border-radius-8" 
                    style="background: linear-gradient(to right, #3c74eb, #95d5f4); border: none;" 
                    @click="handleClick()">立即测额
                </van-button>   
            </div>
        </div>
    </div>
</template>

<script lang='ts' setup>
import { onMounted, ref, onUnmounted } from 'vue'
import type { Ref } from 'vue'
import aicService from '@/service/aicService'
import noData from '@/assets/images/no-data.png'
import { showToast, showFailToast } from 'vant'
import type { ICrmJRApplyParams } from '@/types/businessEntry'
import crmService from '@/service/crmService'
import { useRoute } from 'vue-router'
import { phoneValidate } from '@/utils/validate'

const route = useRoute()
const applyParams = ref<ICrmJRApplyParams>({
    companyName: '',
    intendedAmount: 0,
    mobile: '',
    name: '',
    refCode: '',
    socialCreditCode: '',
    useOfFunds: '',
    verificationCode: '',
})
const showPopup = ref<boolean>(false)
const companyList: Ref<{ text: string, value: string }[]> = ref([])
const name = ref('')
const searchKey = ref('')
const pickLoading = ref(false)
const companyName = ref('')
const fundUse = ref('')
const applyAmount = ref()
const phoneNumber = ref('')
const verifyCode = ref('')

const countdown = ref(0)
const querying = ref(false)
const timer = ref<ReturnType<typeof setInterval> | null>(null)
const buttonLoading = ref<boolean>(false)
const isShowSuccess = ref(false)
const pageInfo: Ref<{ page: number, pageSize: number, total: number }> = ref({
    page: 1,
    pageSize: 10,
    total: 0
})

const getCode = async () => {
    if (!phoneNumber.value) {
        showToast({
            message: '手机号码不能为空',
        })
        return
    }

    if (!phoneValidate(phoneNumber.value.trim())) {
        showToast({
            message: '手机号码格式不正确',
        })
        return
    }

    if (countdown.value > 0 || querying.value) return // 防止重复点击
    querying.value = true
    crmService
        .crmJRSendSmsCode({
            mobile: phoneNumber.value.trim(),
        })
        .then(() => {
            console.log('验证码发送成功')
            // 开始倒计时
            countdown.value = 60
            querying.value = false // 取消 loading
            timer.value = setInterval(() => {
                countdown.value--
                if (countdown.value <= 0 && timer.value) {
                    clearInterval(timer.value)
                    timer.value = null
                }
            }, 1000)
        })
        .catch((err) => {
            console.log(err)
            querying.value = false
        })
}

const handleSearchKeyChange = () => {
    console.log('handleSearchKeyChange')
    pageInfo.value.page = 1
    companyList.value = []
    search()
}
const search = () => {
    if (!searchKey.value) { return }
    pickLoading.value = true
    aicService.searchEnterpriseNoAuth({
        keyword: searchKey.value,
        scope: '0',
        pageSize: 50,
        page: pageInfo.value.page
    }).then((res) => {
        console.log(res)
        companyList.value = res.data.map((item) => {
            return {
                text: item.companyName,
                value: item.socialCreditCode
            }
        })
    }).finally(() => { 
        pickLoading.value = false 
    })
}

const handleSelectCompany = (item: { currentOption: { text: string, value: string } }) => {
    console.log('handleSelectCompany', item.currentOption)
    applyParams.value.companyName = item.currentOption.text
    applyParams.value.socialCreditCode = item.currentOption.value
    companyName.value = item.currentOption.text
    showPopup.value = false
}


const handleClick = () => {
    if (!name.value) {
        showToast({
            message: '请填写姓名',
        })
        return
    }
    if (!companyName.value) {
        showToast({
            message: '请填写公司名称',
        })
        return
    }
    if (!fundUse.value) {
        showToast({
            message: '请填写资金用途',
        })
        return
    }
    if (!applyAmount.value) {
        showToast({
            message: '请填写意向申请额度',
        })
        return
    }
    if (!phoneNumber.value) {
        showToast({
            message: '请填写手机号码',
        })
        return
    }
    if (!verifyCode.value) {
        showToast({
            message: '请填写验证码',
        })
        return
    }
    let params = {
        name:name.value,
        intendedAmount:applyAmount.value,
        mobile:phoneNumber.value,
        useOfFunds:fundUse.value,
        verificationCode:verifyCode.value,
    }
    applyParams.value = {...applyParams.value,...params}
    console.log('applyParams',applyParams.value)
    buttonLoading.value = true
    sessionStorage.setItem('businessEntryInfo', JSON.stringify(applyParams.value))
    crmService.crmJRApply(applyParams.value).then((res) => {
        if(!res.success){
            showFailToast({
                message: res.errMsg || '提交失败'
            })
        }else{
            console.log(res)
            if(res.data.url){
                // window.open(res.data.url,'_blank')
                window.location.href = res.data.url
            }else{
                isShowSuccess.value = true
            }
        }
    }).finally(() => {
        buttonLoading.value = false
    })
}
onUnmounted(() => {
    sessionStorage.removeItem('businessEntryInfo')
})

onMounted(() => {
    console.log('OnlineMeasurement onMounted',sessionStorage.getItem('businessEntryInfo'))
    // 恢复表单数据
    const savedData = sessionStorage.getItem('businessEntryInfo')
    if (savedData) {
        try {
            const parsedData = JSON.parse(savedData)
            name.value = parsedData.name || ''
            companyName.value = parsedData.companyName ||''
            fundUse.value = parsedData.useOfFunds || ''
            applyAmount.value = parsedData.intendedAmount || ''
            phoneNumber.value = parsedData.mobile || ''
            verifyCode.value = parsedData.verificationCode || ''
            applyParams.value = parsedData
            sessionStorage.removeItem('businessEntryInfo')
        } catch (error) {
            console.error('解析保存的数据失败:', error)
        }
    }
    applyParams.value.refCode = route.query.refCode as string
})

</script>

<style lang='scss' scoped>
.field {
    border:1px solid #F2F2F2;
    border-radius: 8px;
    margin-top: 8px;
    :deep(.van-icon){
        font-size: 20px;
    }
}

.van-popup--bottom {
  border-radius: 16px 16px 0 0;
}

.smsBtn {
    font-size: 14px;
    color: var(--main-blue-);
    height: 14px;
    display: flex;
    align-items: center;
    background-color: transparent;
    border: none;
    padding-left: 12px;
}
</style>