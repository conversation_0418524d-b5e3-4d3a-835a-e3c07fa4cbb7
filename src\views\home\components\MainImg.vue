<script lang="ts" setup>
import { onMounted } from 'vue'
import MAINPNG from '@/assets/images/home/<USER>'


onMounted(() => {

})

</script>

<template>
    <div class="home-main-img">
        <img :src="MAINPNG" alt="main" class="height-100" />
    </div>
</template>

<style scoped>
.home-main-img {
    width: 100%;
    height: 229px;
    display: flex;
    justify-content: center;
    align-items: center;
    margin-bottom: 30px;
}
</style>
