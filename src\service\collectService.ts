import http from '@/axios'

export default {
    getReportUrl(body: { socialCreditCode: string, deductType: string }): Promise<{ url: string, isBuy: boolean }> {
        return http.get(`/api/zhenqi-report/collect/get-report-url`, {
            params: body
        })
    },
    getAuthUrl(body: { companyId?: string, companyName?: string, deductType: string, socialCreditCode: string }): Promise<{
        url: string
        requestId: string
    }> {
        return http.get(`/api/zhenqi-report/collect/get-auth-url`, {
            params: body
        })
    }
}