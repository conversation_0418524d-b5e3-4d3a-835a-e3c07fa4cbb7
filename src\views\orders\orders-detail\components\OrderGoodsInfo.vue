<script lang="ts" setup>
import type { IOrderPaymentListItem } from '@/types/order'
import { computed } from 'vue'

const props = defineProps<{
    detail: IOrderPaymentListItem | null
}>()

const orderName = computed(() => {
    const { order_name } = props.detail || {}
    if (!order_name) return ''
    return order_name
})

const quantity = computed(() => {
    const { quantity } = props.detail || {}
    if (!quantity) return ''
    return quantity
})

const isPending = computed(() => {
    const { order_status } = props.detail || {}
    return order_status === '0'
})
</script>

<template>
    <div class="order-goods-info flex flex-column gap-6 back-color-white lr-padding-16 tb-padding-8 border-radius-8">
        <div class="flex flex-row space-between top-bottom-center">
            <div class="font-16 color-black lh-22">{{ orderName }}</div>
            <div class="font-14 color-black" v-if="quantity">x{{ quantity }}</div>
        </div>
        <div class="flex flex-row top-bottom-center gap-4" v-if="isPending">
            <div class="h-16 flex top-bottom-center">
                <van-icon name="volume" class="tips-icon" />
            </div>
            <div class="lh-16 font-12 tips">下单后暂不支持线上退款，请谨慎付款</div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.tips {
    color: #ff854c;
}

.tips-icon {
    color: #ff854c;
    font-size: 16px;
}
</style>
