<template>
    <div class="font-16" @click="copyToClipboard">{{ code }}</div>
</template>

<script setup lang="ts">
import { onMounted } from 'vue'
import { useRoute } from 'vue-router'
const route = useRoute()
const code = route.query.code

const getUrl = (appid: string, state: string, url: string) => {
    let oauthUrl = 'https://weixin.shuzutech.com/get-code-iIa39TfV.html'
    oauthUrl = oauthUrl + '?scope=snsapi_base'
    const appidParam = '&appid=' + appid
    const stateParam = '&state=' + state
    const redirectUriParam = '&redirect_uri=' + encodeURIComponent(url)
    const redirectUrl = oauthUrl + appidParam + stateParam + redirectUriParam

    return redirectUrl
}

const copyToClipboard = async () => {
    if (!code) return
    try {
        await navigator.clipboard.writeText(code.toString())
        console.log('文本已复制到剪贴板')
        return true
    } catch (err) {
        console.error('无法复制文本: ', err)
        return false
    }
}

onMounted(() => {
    if (code) return
    const url = getUrl('wx70e22978a452adcb', 'STATE', 'http://*************:8898/get-code')
    window.open(url)
})
</script>
<style></style>
