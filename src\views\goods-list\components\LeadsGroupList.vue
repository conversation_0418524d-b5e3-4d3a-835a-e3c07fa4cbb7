<script lang="ts" setup>
import orderService from '@/service/orderService'
import CdkInput from '@/views/goods-list/components/CdkInput.vue'
import type { IOrderServicePageItem, IOrderGoodsValidateCouponResult } from '@/types/order'
import { computed, onMounted, ref, watch } from 'vue'
const props = defineProps<{
    createOrder: ({ goodsId, couponCode }: { goodsId: string; quantity: number; couponCode?:string }) => void
}>()

const active = ref(0)
const list = ref<IOrderServicePageItem[]>([])
const loading = ref(true)
const discountInfo = ref<IOrderGoodsValidateCouponResult | null>(null)

const formatPrice = (amount: number) => {
    return (Number(amount) / 100).toFixed(2)
}

const currentPrice = computed(() => {
    // return formatPrice(list.value[active.value].goods.goods_amount)
    return list.value[active.value].goods.goods_amount
})

const createOrder = () => {
    const currentItem = list.value[active.value]
    const { goods_id } = currentItem || {}
    if (discountInfo.value?.coupon_code) {
        props.createOrder({ goodsId: goods_id, quantity: 1, couponCode: discountInfo.value?.coupon_code })
    } else {
        props.createOrder({ goodsId: goods_id, quantity: 1 })
    }
}

const getList = () => {
    loading.value = true
    orderService
        .goodsServiceGoodsPage({
            page: 1,
            pageSize: 99,
            serviceKey: 'applet.lead',
        })
        .then((res) => {
            loading.value = false
            if (res.errCode === 0) {
                list.value = res.data
            }
        })
        .catch(() => {
            loading.value = false
        })
}
watch(() => active.value, (newVal) => {
    console.log('newVal', newVal)
    discountInfo.value = null
})
onMounted(() => {
    getList()
})
const updateDiscountInfo = (val?: IOrderGoodsValidateCouponResult) => {
    if (val) {
        discountInfo.value = val
    } else {
        discountInfo.value = null
    }
}
</script>

<template>
    <template v-if="loading">
        <div class="tb-padding-12">
            <van-skeleton title :row="3" />
        </div>
    </template>
    <template v-if="!loading">
        <div class="flex flex-column height-100">
            <div class="goods-list-page flex flex-row gap-12 flex-wrap">
                <template v-for="(item, index) in list" :key="item.id">
                    <div
                        class="flex flex-column center border-radius-8 tb-padding-12 card"
                        :class="{
                            'active-card': index === active,
                        }"
                        @click="active = index"
                    >
                        <div class="font-16 lh-22 color-black b-margin-8 title">{{ item.goods.goods_name }}</div>
                        <div class="flex flex-row b-margin-5">
                            <div class="flex flex-row baseline gap-2 color-black">
                                <div class="font-20 lh-28 flex color-order">¥</div>
                                <div class="font-28 lh-34 font-weight-500 color-order">
                                    {{ formatPrice(item.goods.goods_amount - item.goods.goods_discount) }}
                                </div>
                            </div>
                        </div>
                        <div class="font-12 lh-18 color-three-grey">{{ item.goods.goods_desc }}</div>
                    </div>
                </template>
            </div>
            <div class="flex flex-1 flex-row-end flex-column">
                <div class="display-flex flex-row-reverse b-margin-6">
                    <CdkInput :original-amount="currentPrice" :goods-ids="list[active].goods_id" @updateDiscountInfo="updateDiscountInfo" :code="discountInfo?.coupon_code"/>
                </div>
                <div class="small-primary-btn flex width-100 h-46 font-18  " @click="createOrder">
                    立即支付 ¥{{ discountInfo?.coupon_id ? formatPrice(discountInfo?.final_amount) : formatPrice(currentPrice || 0) }}
                    <span v-if="discountInfo?.original_amount" class="l-margin-6 t-padding-2 font-14 font-weigth-500" style="opacity: 0.7; text-decoration: line-through;">￥{{ formatPrice(discountInfo?.original_amount) }}</span>
                </div>
            </div>
        </div>
    </template>
</template>

<style scoped>
.active-card {
    border: 1px solid var(--order-color) !important;
    background-color: #ba76331a;
}

.active-card .title {
    color: var(--order-color);
}

.card {
    flex: 0 0 calc(50% - 6px);
    box-sizing: border-box;
    max-height: 110px;
    border: 1px solid var(--border-color);
}

.card .title {
    font-weight: 500;
}
</style>
