import type { ICommonResponse } from './axios'

export interface IServiceDetailResponse {
    id: string
    createUser: string
    isOpen: boolean
    rules: IServiceDetailRules[]
    tenantId: string
    type: number
    createTime: number
    updateTime: number
}

export interface IServiceDetailRules {
    num: number
    allUser: boolean
    users: string[]
}

export interface IServiceSwitchRequest {
    isOpen: boolean
    type: number
}

export interface IServiceEditRequest {
    id: string
    isOpen: boolean
    rules: IServiceDetailRules[]
    type: number
}

export interface IServiceAddRequest {
    id: string
    isOpen: boolean
    rules: IServiceDetailRules
    type: number
}

export interface IServiceEditResponse extends ICommonResponse {
    data: object
}
