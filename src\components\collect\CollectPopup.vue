<template>
    <!-- 授权链接 -->
    <van-popup v-model:show="showCollect" :style="{ padding: '16px', borderRadius: '8px 8px 0 0' }" position='bottom'>
        <div class="backcolor-white text-center border-radius-12">
            <div class="display-flex relative left-right-center b-padding-15 top-bottom-center">
                <div class="absolute" style="right: 0;top:-10px" @click="showCollect = false">

                    <icon icon="icon-x-02" :size="12" color="" />
                </div>
                <div class="font-16">认证服务</div>
            </div>
            <div class="display-flex tb-padding-15 gap-6">
                <div class="lr-padding-10 display-flex top-bottom-center  btn border-radius-8 flex-1"
                     @click="goUrlCollect()" style="height: 1.5rem;">

                    <icon icon="icon-renzheng" :size="23" color="#ffffff" />

                    <div class="border-radius-8 flex-1 display-flex flex-column">
                        <div class="font-16">直接认证</div>
                        <div class="font-12 t-margin-5">账密/税局APP扫码</div>
                    </div>
                </div>
                <div class="lr-padding-10 display-flex top-bottom-center scan-box border-radius-8 flex-1"
                     @click="goQrcodeCollect()">

                    <icon icon="icon-scan1" :size="23" color="#ffffff" />

                    <div class="border-radius-8 flex-1">
                        <div class="font-16">面对面扫码</div>
                        <div class="font-12 t-margin-5">微信/支付宝/浏览器</div>
                    </div>
                </div>
            </div>
            <div class="b-padding-30">
                <div class="border text-center tb-padding-5 flex-center">
                    <icon icon="icon-Frame2" :size="16" color="#3C74EB" /> <span class="font-16"
                                                                                 @click="copyUrl">复制链接给好友认证</span>
                </div>

            </div>
        </div>
    </van-popup>
    <!-- 面对面扫码 -->
    <van-popup v-model:show="showQrcodeCollect" :style="{ padding: '16px', borderRadius: '8px', width: '80% ' }">
        <div class="display-flex relative left-right-center b-padding-15 top-bottom-center">
            <div class="absolute" style="right: 0;top:-10px" @click="showQrcodeCollect = false">

                <icon icon="icon-a-Frame1171276285" :size="12" color="" />
            </div>
            <div class="font-16">认证服务</div>
        </div>

        <div class="back-color-blue color-white text-center border-radius-8 font-16 tb-padding-10">
            <div>{{ companyName }}</div>
            <div class="t-margin-4">{{ socialCreditCode }}</div>
        </div>

        <div id="collectQrcode" v-if="collectUrl" class="t-margin-10 lr-paddding-10 display-flex left-right-center">
            <vue-qr :text="collectUrl" :size="200"></vue-qr>
        </div>

        <div class="font-14 text-center t-margin-10">
            <div>请使用 <span class="color-blue">微信</span>/<span class="color-blue">支付宝</span>进行 </div>
            <div>扫码登录认证页面 </div>
        </div>
        <div class="font-14 t-margin-10">
            <div>温馨提示：</div>
            <div>1：此二维码24小时内有效请尽快在有效期内完成扫码</div>
            <div>2：扫码前，请确保您的手机网络连接稳定且信号良好</div>
            <div>3：操作过程中，如遇到任何问题或困难，请及时联系客服人员</div>
        </div>
    </van-popup>

</template>


<script lang="ts" setup>

import { ref, defineExpose, computed } from 'vue'
import collectService from '@/service/collectService'
import orderService from '@/service/orderService'
import { useRoute, useRouter } from 'vue-router'
import vueQr from 'vue-qr/src/packages/vue-qr.vue'
import { showToast, showConfirmDialog } from 'vant'
import { useStore } from 'vuex'
import type { RootState } from '@/types/store'

const router = useRouter()
const route = useRoute()
const isPreset = computed(() => {
    const { query } = route
    const { preset } = query || {}
    return preset ? true : false
})
const showCollect = ref<boolean>(false)
const collectUrl = ref<string>('')


const store = useStore<RootState>()
const oemInfo = computed(() => {
    const { oemConfig } = store.state.auth || {}
    const { modules } = oemConfig || {}
    const { config } = modules?.[0] || {}
    return config
})


interface CollectInfo {
    socialCreditCode: string
    companyName: string
    deductType: string,
    isBuy?: boolean
}

const socialCreditCode = ref<string>('')
const companyName = ref<string>('')
const ZQY_YUNYING_URL= import.meta.env.VITE_ZQY_YUNYING_API

const getCollectUrl = async (info: CollectInfo) => {
    // 如果是示例数据
    if (isPreset.value) {
        collectUrl.value = `${ZQY_YUNYING_URL}stcs/collect?qymc=${info.companyName}&nsrsbh=${info.socialCreditCode}&mode=demo`
        showCollect.value = true
    } else {
        try {
            if (!info.deductType) {
                throw new Error('缺少扣费项目')
            }

            if (!info.socialCreditCode) {
                throw new Error('缺少企业税号')
            }

            if (!info.companyName) {
                throw new Error('缺少企业名称')
            }

            socialCreditCode.value = info.socialCreditCode
            companyName.value = info.companyName


            let lessRes = await orderService.orderServiceStatistics({ serviceKeys: info.deductType })
            if (lessRes[0] && Number(lessRes[0].num) <= 0 && !info.isBuy) {
                router.push({
                    name: 'goodsList',
                    query: {
                        goods: 'combo-group',
                    },
                })
                return
            }

            collectService.getAuthUrl({
                socialCreditCode: info.socialCreditCode,
                companyName: info.companyName,
                deductType: info.deductType
            }).then(res => {
                console.log(res)
                collectUrl.value = res.url
                if (oemInfo.value?.easyCollect || info.isBuy) {
                    showCollect.value = true
                } else {
                    showConfirmDialog({
                        title: '提示',
                        message:
                            '本次授权成功后,会扣除一份报告权益,是否确认生成报告链接?',
                    })
                        .then(() => {
                            showCollect.value = true
                        })
                        .catch(() => {
                        })
                }
            }).finally(() => {
            })

        } catch (err) {
            showToast(err.message)
        }
    }
}

const goUrlCollect = () => {
    router.push({
        name: 'authorize',
        query: {
            url: collectUrl.value,
        }
    })
    // window.location.href = collectUrl.value
}

const showQrcodeCollect = ref<boolean>(false)

const goQrcodeCollect = () => {
    showCollect.value = false
    showQrcodeCollect.value = true
}

const copyUrl = () => {
    navigator.clipboard.writeText(collectUrl.value).then(function () {
        showToast('复制成功')
        showCollect.value = false
    }).catch(function (err) {
        showToast(`无法复制文本:${err}`)
        console.error()
    })
}

defineExpose({
    getCollectUrl
})

</script>


<style scoped lang="scss">
.collect-btn {
    bottom: 32px;
    width: calc(100vw - 32px);
}

.icon {
    width: 25px;
    height: 25px
}

.scan-box {
    background: linear-gradient(98.52deg, #ffcd97 2.65%, #ff8a3c 93.37%);
    color: #fff;
}

.close {
    width: 20px;
    height: 20px;
}

.collectQrcode {

    width: 200px;
    height: 200px;

}
</style>