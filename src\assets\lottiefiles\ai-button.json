{"v": "5.8.1", "fr": 60, "ip": 0, "op": 121, "w": 400, "h": 200, "nm": "Frame 1171276478", "ddd": 0, "assets": [{"id": "image_0", "w": 300, "h": 300, "u": "", "p": "data:image/png;base64,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", "e": 1}, {"id": "comp_0", "nm": "Frame 1171276477", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Frame 1171276463", "refId": "comp_1", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [58, 23.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [58, 8.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 116, "h": 17, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Ellipse 840", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [0]}, {"t": 120, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [28, 4, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 60, "s": [60, 60, 100]}, {"t": 120, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [8, 8], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456580519676, 0.631011545658, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 840", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Frame 1171276477", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [58, 16, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [116, 32], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Frame 1171276477", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_1", "nm": "Frame 1171276463", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 5, "nm": "关联风险专项检查", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [69, 12.25, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "t": {"d": {"k": [{"s": {"sz": [96, 17], "ps": [-48, -8.5], "s": 12, "f": "PingFangSC-Regular", "t": "关联风险专项检查", "ca": 0, "j": 2, "tr": 0, "lh": 1.41667008399963, "ls": 0, "fc": [0.457, 0.631, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Frame 5", "refId": "comp_2", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [8, 8.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [8, 8, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 16, "h": 16, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Frame 1171276463", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [58, 8.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [116, 17], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Frame 1171276463", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_2", "nm": "Frame 5", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [8, 8, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-0.324, 0.001], [0, 0], [-0.229, -0.229], [-0.001, -0.324], [0, 0], [0.229, -0.229], [0.324, -0.001], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.229, 0.229], [0.001, 0.324], [0, 0], [-0.229, 0.229]], "o": [[0, 0], [0.324, 0.001], [0.229, 0.229], [0, 0], [-0.001, 0.324], [-0.229, 0.229], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.324, -0.001], [-0.229, -0.229], [0, 0], [0.001, -0.324], [0.229, -0.229]], "v": [[-6.111, -7.333], [6.111, -7.333], [6.974, -6.974], [7.333, -6.111], [7.333, 3.667], [6.974, 4.53], [6.111, 4.889], [0.611, 4.889], [0.611, 6.111], [2.444, 6.111], [2.444, 7.333], [-2.444, 7.333], [-2.444, 6.111], [-0.611, 6.111], [-0.611, 4.889], [-6.111, 4.889], [-6.974, 4.53], [-7.333, 3.667], [-7.333, -6.111], [-6.974, -6.974]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4.278, -3.667], [3.056, -3.667], [3.056, -1.222], [4.278, -1.222]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[1.833, -3.667], [0.611, -3.667], [0.611, -1.222], [1.833, -1.222]], "c": true}, "ix": 2}, "nm": "路径 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-0.611, -3.667], [-1.833, -3.667], [-1.833, -1.222], [-0.611, -1.222]], "c": true}, "ix": 2}, "nm": "路径 4", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-3.056, -3.667], [-4.278, -3.667], [-4.278, -1.222], [-3.056, -1.222]], "c": true}, "ix": 2}, "nm": "路径 5", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.369, 1.375], [-4.186, 2.597], [4.889, 1.222], [4.706, 0]], "c": true}, "ix": 2}, "nm": "路径 6", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456580519676, 0.631011545658, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Vector", "np": 8, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "<PERSON>ame", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [8, 8, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [16, 16], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "<PERSON>ame", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_3", "nm": "Frame 1171276464", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Frame 1171276462", "refId": "comp_4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [46, 22.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [46, 8.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 92, "h": 17, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Ellipse 839", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [0]}, {"t": 120, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [46, 4, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [60, 60, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 60, "s": [100, 100, 100]}, {"t": 120, "s": [60, 60, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [8, 8], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456580519676, 0.631011545658, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 839", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Frame 1171276464", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [46, 15.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [92, 31], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Frame 1171276464", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_4", "nm": "Frame 1171276462", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 5, "nm": "发票专项检查", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [56, 12, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "t": {"d": {"k": [{"s": {"sz": [72, 17], "ps": [-36, -8.5], "s": 12, "f": "PingFangSC-Regular", "t": "发票专项检查", "ca": 0, "j": 2, "tr": 0, "lh": 1.41667008399963, "ls": 0, "fc": [0.457, 0.631, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Frame 4", "refId": "comp_5", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [8, 8.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [8, 8, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 16, "h": 16, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Frame 1171276462", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [46, 8.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [92, 17], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Frame 1171276462", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_5", "nm": "Frame 4", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [7.865, 7.898, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.328, 0.328], [0.501, 0.009], [0, 0], [0.328, -0.328], [0.009, -0.501], [0, 0], [-0.328, -0.328], [-0.501, -0.009], [0, 0], [-0.328, 0.328], [-0.009, 0.501]], "o": [[-0.009, -0.501], [-0.328, -0.328], [0, 0], [-0.501, 0.009], [-0.328, 0.328], [0, 0], [0.009, 0.501], [0.328, 0.328], [0, 0], [0.501, -0.009], [0.328, -0.328], [0, 0]], "v": [[6.207, -4.457], [5.701, -5.701], [4.457, -6.207], [-4.457, -6.207], [-5.701, -5.701], [-6.207, -4.457], [-6.207, 4.457], [-5.701, 5.701], [-4.457, 6.207], [4.457, 6.207], [5.701, 5.701], [6.207, 4.457]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-0.005, -0.141], [0.077, -0.128], [0.136, -0.009], [0, 0], [0.105, 0.105], [0, 0.141], [-0.105, 0.128], [-0.137, 0.009], [0, 0], [-0.1, -0.1]], "o": [[0.005, 0.141], [-0.078, 0.128], [0, 0], [-0.136, 0], [-0.105, -0.105], [0, -0.141], [0.104, -0.127], [0, 0], [0.136, 0.009], [0.1, 0.1]], "v": [[4.368, 3.152], [4.259, 3.555], [3.938, 3.76], [-3.937, 3.76], [-4.299, 3.603], [-4.456, 3.234], [-4.299, 2.831], [-3.937, 2.626], [3.856, 2.626], [4.211, 2.79]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0.077, 0.078], [0, 0.132], [-0.078, 0.078], [-0.137, 0.009], [0, 0], [0, 0], [0, 0.11], [-0.091, 0.091], [-0.11, 0], [-0.091, -0.091], [0, 0], [0, 0], [-0.11, 0], [-0.091, -0.091], [0, -0.11], [0.091, -0.091], [0, 0], [0, 0], [-0.078, -0.077], [0, -0.132], [0.077, -0.077], [0.137, 0], [0, 0], [0, 0], [0, 0], [-0.078, -0.077], [0, -0.132], [0.077, -0.082], [0.137, 0], [0, 0], [0, 0], [0.077, -0.077], [0.136, 0], [0.077, 0.078], [0, 0.137], [0, 0], [0, 0], [0.077, 0.082], [0, 0.132], [-0.078, 0.078], [-0.137, 0]], "o": [[0, 0], [0, 0], [-0.136, 0], [-0.077, -0.077], [0, -0.132], [0.077, -0.077], [0, 0], [0, 0], [-0.091, -0.091], [0, -0.11], [0.091, -0.091], [0.11, 0], [0, 0], [0, 0], [0.091, -0.091], [0.11, 0], [0.091, 0.091], [0, 0.11], [0, 0], [0, 0], [0.136, 0.009], [0.077, 0.077], [0, 0.132], [-0.077, 0.077], [0, 0], [0, 0], [0, 0], [0.136, 0], [0.077, 0.077], [0, 0.132], [-0.077, 0.082], [0, 0], [0, 0], [0, 0.136], [-0.077, 0.077], [-0.136, 0], [-0.077, -0.077], [0, 0], [0, 0], [-0.136, 0], [-0.077, -0.082], [0, -0.132], [0.077, -0.077], [0, 0]], "v": [[-0.437, -0.697], [-0.437, -1.23], [-1.75, -1.23], [-2.071, -1.346], [-2.187, -1.66], [-2.071, -1.974], [-1.75, -2.104], [-0.957, -2.104], [-1.832, -2.979], [-1.969, -3.28], [-1.832, -3.581], [-1.531, -3.718], [-1.23, -3.581], [0, -2.364], [1.23, -3.581], [1.531, -3.718], [1.832, -3.581], [1.969, -3.28], [1.832, -2.979], [0.957, -2.104], [1.75, -2.104], [2.071, -1.974], [2.187, -1.66], [2.071, -1.346], [1.75, -1.23], [0.438, -1.23], [0.438, -0.697], [1.748, -0.697], [2.069, -0.581], [2.185, -0.267], [2.069, 0.054], [1.748, 0.177], [0.436, 0.177], [0.436, 0.874], [0.32, 1.195], [-0.001, 1.311], [-0.322, 1.195], [-0.438, 0.874], [-0.438, 0.177], [-1.75, 0.177], [-2.071, 0.054], [-2.187, -0.267], [-2.071, -0.581], [-1.75, -0.697]], "c": true}, "ix": 2}, "nm": "路径 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456580519676, 0.631011545658, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Vector", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "<PERSON>ame", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [8, 8, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [16, 16], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "<PERSON>ame", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_6", "nm": "Frame 1171276461", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 5, "nm": "税务专项检查", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [55.5, 12, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "t": {"d": {"k": [{"s": {"sz": [72, 17], "ps": [-36, -8.5], "s": 12, "f": "PingFangSC-Regular", "t": "税务专项检查", "ca": 0, "j": 2, "tr": 0, "lh": 1.41667008399963, "ls": 0, "fc": [0.457, 0.631, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Frame 3", "refId": "comp_7", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [8, 8.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [8, 8, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 16, "h": 16, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Frame 1171276461", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [46, 8.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [92, 17], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Frame 1171276461", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_7", "nm": "Frame 3", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [8.044, 4.874, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0.403, -0.264], [0.283, 0.082]], "o": [[0.097, 0.148], [-0.384, 0.251], [0, 0]], "v": [[-0.265, 0.398], [-0.37, 1.086], [-1.47, 1.185]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-0.194, -0.059], [0, 0], [-0.376, 0.245]], "o": [[0, 0], [-0.105, -0.137], [0.411, -0.268]], "v": [[1.47, -1.176], [0.334, -0.433], [0.389, -1.098]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456580519676, 0.631011545658, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Vector", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [7.999, 4.884, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.281, 1.171], [-2.007, 1.315], [-2.277, -1.174], [2.007, -1.313]], "o": [[-2.269, -1.18], [2.016, -1.309], [2.271, 1.176], [-2.016, 1.309]], "v": [[-3.642, 2.376], [-4.119, -2.128], [3.641, -2.373], [4.118, 2.129]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-0.12, 0.078], [0, 0], [-0.967, 0.635], [0.291, 0.411], [0, 0], [-0.049, -0.025], [-0.049, 0.015], [0, 0], [-0.029, 0.019], [0.217, 0.127], [0, 0], [0.139, 0.072], [0, 0], [0.118, -0.078], [0, 0], [1.026, -0.669], [-0.293, -0.371], [0, 0], [0.048, 0.025], [0.021, -0.006], [0, 0], [0.021, -0.013], [-0.266, -0.137], [0, 0], [-0.143, -0.074], [0, 0], [0, 0]], "o": [[0, 0], [0.599, 0.219], [0.893, -0.582], [0, 0], [0.34, 0.177], [0.032, 0.017], [0, 0], [0.046, -0.01], [0.238, -0.154], [0, 0], [0.12, -0.076], [0, 0], [-0.143, -0.074], [0, 0], [-0.376, -0.179], [-0.92, 0.601], [0, 0], [-0.433, -0.279], [-0.032, -0.017], [0, 0], [-0.057, 0.019], [-0.209, 0.137], [0, 0], [-0.118, 0.078], [0, 0], [0, 0], [0.141, 0.072]], "v": [[-2.441, 1.901], [-2.127, 1.695], [0.621, 1.515], [0.655, -0.121], [1.946, -0.963], [2.565, -0.315], [2.693, -0.315], [3.411, -0.498], [3.514, -0.536], [2.679, -1.438], [3.021, -1.661], [2.999, -1.836], [2.839, -1.919], [2.533, -1.914], [2.191, -1.691], [-0.529, -1.539], [-0.494, 0.059], [-1.861, 0.95], [-2.596, 0.196], [-2.724, 0.196], [-3.539, 0.46], [-3.617, 0.492], [-2.617, 1.443], [-2.931, 1.648], [-2.902, 1.825], [-2.788, 1.884], [-2.745, 1.906]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456580519676, 0.631011545658, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Vector", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [7.998, 4.885, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[2.837, 1.465], [-0.067, 1.146], [-1.097, 0.715], [0, 0], [-1.036, 0.217], [-0.006, 0.002], [-0.207, 0.03], [-0.008, 0.002], [-0.209, 0.019], [-0.01, 0], [-0.211, 0.008], [0, 0], [-0.124, 0], [-1.317, -0.68], [-0.106, -1.019], [-0.002, -0.07], [0.004, -0.055], [1.1, -0.718], [0, 0]], "o": [[-1.596, -0.825], [0.051, -0.891], [0, 0], [0.781, -0.509], [0.006, -0.002], [0.201, -0.042], [0.008, 0], [0.205, -0.029], [0.01, 0], [0.211, -0.019], [0, 0], [0.122, -0.004], [1.629, 0], [1.42, 0.734], [0.006, 0.07], [0, 0.055], [-0.051, 0.893], [0, 0], [-2.511, 1.634]], "v": [[-4.543, 2.965], [-6.854, -0.163], [-5.139, -2.654], [-5.137, -2.656], [-2.361, -3.741], [-2.346, -3.745], [-1.734, -3.853], [-1.709, -3.857], [-1.088, -3.929], [-1.061, -3.931], [-0.428, -3.969], [-0.407, -3.969], [-0.04, -3.981], [4.545, -2.966], [6.849, -0.216], [6.853, -0.007], [6.856, 0.16], [5.14, 2.658], [5.138, 2.659]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-2.433, -1.256], [-2.151, 1.403], [2.431, 1.254], [2.151, -1.403]], "o": [[2.431, 1.256], [2.151, -1.404], [-2.433, -1.258], [-2.151, 1.403]], "v": [[-3.893, 2.538], [4.405, 2.274], [3.895, -2.54], [-4.4, -2.276]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456580519676, 0.631011545658, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Vector", "np": 4, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [7.996, 9.055, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.45, 0.941], [0, 0], [-0.323, 0.382], [1.055, -0.688], [0, 0], [1.881, 0], [1.317, 0.68], [0.105, 1.021], [-0.787, -0.407], [-1.83, 0]], "o": [[0, 0], [0.487, -0.317], [-0.089, 0.855], [0, 0], [-1.346, 0.876], [-1.629, 0], [-1.423, -0.737], [0.469, 0.563], [1.391, 0.72], [2.142, 0]], "v": [[5.615, -0.761], [5.628, -0.771], [6.843, -1.824], [5.142, 0.562], [5.14, 0.563], [0.047, 1.886], [-4.539, 0.871], [-6.843, -1.886], [-4.948, -0.417], [0.047, 0.698]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456580519676, 0.631011545658, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [7.996, 11.137, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[-1.45, 0.941], [0, 0], [-0.323, 0.382], [1.055, -0.688], [0, 0], [1.881, 0], [1.317, 0.68], [0.105, 1.021], [-0.787, -0.407], [-1.83, 0]], "o": [[0, 0], [0.487, -0.317], [-0.089, 0.855], [0, 0], [-1.346, 0.876], [-1.629, 0], [-1.423, -0.735], [0.469, 0.561], [1.391, 0.72], [2.142, 0]], "v": [[5.615, -0.762], [5.628, -0.772], [6.843, -1.824], [5.142, 0.561], [5.14, 0.563], [0.047, 1.885], [-4.539, 0.87], [-6.843, -1.885], [-4.948, -0.418], [0.047, 0.697]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456580519676, 0.631011545658, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [7.996, 13.218, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.325, 0.38], [1.055, -0.686], [0, 0], [1.881, 0], [1.317, 0.68], [0.105, 1.021], [-0.787, -0.407], [-1.83, 0], [-1.45, 0.941]], "o": [[0.485, -0.317], [-0.089, 0.853], [0, 0], [-1.346, 0.876], [-1.629, 0], [-1.423, -0.735], [0.469, 0.559], [1.391, 0.72], [2.142, 0], [0, 0]], "v": [[5.628, -0.772], [6.843, -1.823], [5.142, 0.561], [5.14, 0.563], [0.047, 1.885], [-4.539, 0.87], [-6.843, -1.885], [-4.948, -0.418], [0.047, 0.697], [5.615, -0.762]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456580519676, 0.631011545658, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "<PERSON>ame", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [8, 8, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [16, 16], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "<PERSON>ame", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_8", "nm": "Frame 1171276466", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Frame 1171276460", "refId": "comp_9", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [46, 8.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [46, 8.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 92, "h": 17, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Ellipse 837", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [0]}, {"t": 120, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [57.5, 28, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [60, 60, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 60, "s": [100, 100, 100]}, {"t": 120, "s": [60, 60, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [8, 8], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456580519676, 0.631011545658, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 837", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Frame 1171276466", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [46, 16, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [92, 32], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Frame 1171276466", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_9", "nm": "Frame 1171276460", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 5, "nm": "财务专项检查", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [55.125, 12.125, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "t": {"d": {"k": [{"s": {"sz": [72, 17], "ps": [-36, -8.5], "s": 12, "f": "PingFangSC-Regular", "t": "财务专项检查", "ca": 0, "j": 2, "tr": 0, "lh": 1.41667008399963, "ls": 0, "fc": [0.457, 0.631, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Frame 2", "refId": "comp_10", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [8, 8.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [8, 8, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 16, "h": 16, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Frame 1171276460", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [46, 8.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [92, 17], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Frame 1171276460", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_10", "nm": "Frame 2", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [8, 8, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.866, 0], [1.157, -2.272], [0, 0], [0, 0], [0, 0], [-0.109, 0.03], [-0.099, -0.055], [0, 0], [-0.037, -0.045], [-0.017, -0.056], [0.006, -0.058], [0.028, -0.051], [0.046, -0.036], [0.056, -0.016], [0.058, 0.007], [0.05, 0.029], [0, 0], [0, 0], [0.106, -0.031], [0.099, 0.049], [0, 0], [0, -0.831], [-3.866, 0], [-1.157, 2.272], [0, 0], [0, 0], [0.106, -0.031], [0.099, 0.049], [0, 0], [0.034, 0.108], [-0.051, 0.102], [-0.107, 0.038], [-0.103, -0.048], [0, 0], [0, 0], [-0.106, 0.031], [-0.099, -0.049], [0, 0], [0, 0.831]], "o": [[-2.724, 0], [0, 0], [0, 0], [0, 0], [0.058, -0.097], [0.109, -0.03], [0, 0], [0.051, 0.027], [0.037, 0.045], [0.017, 0.056], [-0.006, 0.058], [-0.028, 0.051], [-0.046, 0.036], [-0.056, 0.016], [-0.058, -0.007], [0, 0], [0, 0], [-0.057, 0.095], [-0.106, 0.031], [0, 0], [-0.266, 0.74], [0, 3.866], [2.724, 0], [0, 0], [0, 0], [-0.057, 0.094], [-0.106, 0.031], [0, 0], [-0.1, -0.054], [-0.034, -0.108], [0.051, -0.102], [0.107, -0.038], [0, 0], [0, 0], [0.057, -0.094], [0.106, -0.031], [0, 0], [0.266, -0.74], [0, -3.866]], "v": [[0, -7], [-6.241, -3.172], [-6.241, -3.172], [-2.791, -1.448], [-1.688, -3.287], [-1.427, -3.485], [-1.102, -3.446], [3.273, -1.04], [3.407, -0.93], [3.488, -0.778], [3.504, -0.606], [3.452, -0.442], [3.341, -0.31], [3.187, -0.231], [3.015, -0.218], [2.852, -0.273], [-1.154, -2.476], [-2.25, -0.65], [-2.503, -0.455], [-2.821, -0.484], [-6.589, -2.368], [-7, 0], [0, 7], [6.241, 3.172], [2.792, 1.448], [1.687, 3.287], [1.435, 3.482], [1.117, 3.454], [-3.258, 1.266], [-3.468, 1.013], [-3.442, 0.685], [-3.195, 0.468], [-2.867, 0.484], [1.146, 2.49], [2.25, 0.65], [2.503, 0.455], [2.821, 0.484], [6.589, 2.368], [7, 0]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456580519676, 0.631011545658, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Vector", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "<PERSON>ame", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [8, 8, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [16, 16], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "<PERSON>ame", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_11", "nm": "Frame 1171276459", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 5, "nm": "经营基本面专项检查", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [73.75, 12.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "t": {"d": {"k": [{"s": {"sz": [108, 17], "ps": [-54, -8.5], "s": 12, "f": "PingFangSC-Regular", "t": "经营基本面专项检查", "ca": 0, "j": 2, "tr": 0, "lh": 1.41667008399963, "ls": 0, "fc": [0.457, 0.631, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "<PERSON>ame", "refId": "comp_12", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [8, 8.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [8, 8, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 16, "h": 16, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Frame 1171276459", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [64, 8.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [128, 17], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Frame 1171276459", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_12", "nm": "<PERSON>ame", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Vector", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [8, 8, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0.11, 0.115], [0.159, 0.006], [0, 0], [0.078, -0.029], [0.06, -0.057], [0.033, -0.076], [0, -0.083], [-0.033, -0.076], [-0.06, -0.057], [-0.078, -0.029], [-0.083, 0.003], [0, 0], [-0.11, 0.115], [0, 0.159]], "o": [[-0.11, -0.115], [0, 0], [-0.083, -0.003], [-0.078, 0.029], [-0.06, 0.057], [-0.033, 0.076], [0, 0.083], [0.033, 0.076], [0.06, 0.057], [0.078, 0.029], [0, 0], [0.159, -0.006], [0.11, -0.115], [0, -0.159]], "v": [[5.343, 5.112], [4.924, 4.923], [-4.924, 4.923], [-5.167, 4.962], [-5.376, 5.094], [-5.516, 5.297], [-5.565, 5.538], [-5.516, 5.78], [-5.376, 5.983], [-5.167, 6.114], [-4.924, 6.154], [4.924, 6.154], [5.343, 5.965], [5.515, 5.538]], "c": true}, "ix": 2}, "nm": "路径 1", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0.803, 0], [0, 0], [0, -0.803], [0, 0], [-0.273, -0.273], [-0.386, 0], [0, 0], [0, 0.803]], "o": [[0, -0.803], [0, 0], [-0.803, 0], [0, 0], [0, 0.386], [0.273, 0.273], [0, 0], [0.803, 0], [0, 0]], "v": [[6.154, -4.7], [4.7, -6.154], [-4.7, -6.154], [-6.154, -4.7], [-6.154, 2.238], [-5.728, 3.266], [-4.7, 3.692], [4.7, 3.692], [6.154, 2.238]], "c": true}, "ix": 2}, "nm": "路径 2", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[-4.3, -0.677], [-1.366, -3.8], [1.48, -0.663], [3.477, -2.72], [4.315, -1.827], [1.453, 1.123], [-1.38, -1.999], [-3.448, 0.201]], "c": true}, "ix": 2}, "nm": "路径 3", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ty": "mm", "mm": 1, "nm": "合并路径 1", "mn": "ADBE Vector Filter - Merge", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456580519676, 0.631011545658, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Vector", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "<PERSON>ame", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [8, 8, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [16, 16], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "<PERSON>ame", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_13", "nm": "Frame 1171276474", "fr": 60, "layers": [{"ddd": 0, "ind": 1, "ty": 4, "nm": "Ellipse 845", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 1, "s": [202, 99, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [190.75, 111.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [202.5, 98.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [4, 4], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.329411774874, 0.623529434204, 0.988235294819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 845", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 4, "nm": "Ellipse 853", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [96.5, 154.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 32, "s": [109.5, 156, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [112.5, 144.75, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [100.5, 144.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [96.75, 155, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [3, 3], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.329411774874, 0.623529434204, 0.988235294819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 853", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 4, "nm": "Ellipse 852", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [89.5, 116.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [97.25, 120.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [89.25, 116.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [3, 3], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.329411774874, 0.623529434204, 0.988235294819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 852", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 4, "nm": "Ellipse 851", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [81.5, 109.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [86, 104.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [91.75, 105.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [90, 111.75, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [81.25, 109.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [3, 3], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.329411774874, 0.623529434204, 0.988235294819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 851", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Ellipse 850", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [84.5, 76.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [84.25, 87.75, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [91.75, 88.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 90, "s": [91, 79.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [84.25, 76.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [3, 3], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.329411774874, 0.623529434204, 0.988235294819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 850", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": -4, "op": 296, "st": -4, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 4, "nm": "Ellipse 849", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [96.5, 73.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [96.75, 83, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [96.25, 73, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [3, 3], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.329411774874, 0.623529434204, 0.988235294819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 849", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 4, "nm": "Ellipse 856", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [182.5, 142.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [170.75, 140.75, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [183.5, 142.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [3, 3], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.329411774874, 0.623529434204, 0.988235294819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 856", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 4, "nm": "Ellipse 855", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [184.5, 133.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [182.75, 124.75, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [184.25, 133.5, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [3, 3], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.329411774874, 0.623529434204, 0.988235294819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 855", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Ellipse 848", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [50]}, {"t": 120, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [171.5, 60.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [165.5, 63.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [172.25, 60.25, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [3, 3], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.329411774874, 0.623529434204, 0.988235294819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 848", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 4, "nm": "Ellipse 847", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [168.5, 55.5, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [155.75, 56.75, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [169.25, 55.25, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [3, 3], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.329411774874, 0.623529434204, 0.988235294819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 847", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Ellipse 844", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [192, 93, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [188, 98, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [192.5, 92.75, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [4, 4], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.329411774874, 0.623529434204, 0.988235294819, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 844", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 12, "ty": 4, "nm": "Ellipse 846", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"t": 60, "s": [50]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [100, 64, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [107.75, 64, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [108.75, 68, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [102.75, 69.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [100.5, 64.25, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [6, 6], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.066666670144, 0.517647087574, 0.972549021244, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 846", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 13, "ty": 4, "nm": "Ellipse 854", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [50]}, {"t": 120, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [153.375, 160.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [156.375, 155.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 60, "s": [151, 151.25, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [138.5, 158.125, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [153.625, 160.375, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [6, 6], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.066666670144, 0.517647087574, 0.972549021244, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 854", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 14, "ty": 4, "nm": "Ellipse 843", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 61, "s": [50]}, {"t": 120, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 1, "k": [{"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 0, "s": [194, 80, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 30, "s": [185.75, 73, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 61, "s": [184, 82, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"i": {"x": 0.833, "y": 0.833}, "o": {"x": 0.167, "y": 0.167}, "t": 91, "s": [190.5, 85, 0], "to": [0, 0, 0], "ti": [0, 0, 0]}, {"t": 120, "s": [194.5, 80, 0]}], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [6, 6], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.066666670144, 0.517647087574, 0.972549021244, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 843", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 15, "ty": 0, "nm": "Frame 1171276473", "refId": "comp_14", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [140, 100.107, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [40, 16.5, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 60, "s": [120, 120, 100]}, {"t": 120, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "w": 80, "h": 33, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 16, "ty": 2, "nm": "1 1", "refId": "image_0", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [80]}, {"t": 60, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [141, 103, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [150, 150, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [33.333, 33.333, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 58, "s": [30, 30, 100]}, {"t": 120, "s": [33, 33, 100]}], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}]}, {"id": "comp_14", "nm": "Frame 1171276473", "fr": 60, "layers": [{"ddd": 0, "ind": 3, "ty": 5, "nm": "AI", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [32.75, 12, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "t": {"d": {"k": [{"s": {"s": 14, "f": "DingTalk-JinBuTi", "t": "AI", "ca": 0, "j": 0, "tr": 39, "lh": 1.42857003211975, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 5, "nm": "分析助手", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [12, 29.75, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "t": {"d": {"k": [{"s": {"s": 14, "f": "TencentSansW7", "t": "分析助手", "ca": 0, "j": 0, "tr": 39, "lh": 1.42857003211975, "ls": 0, "fc": [1, 1, 1]}, "t": 0}]}, "p": {}, "m": {"g": 1, "a": {"a": 0, "k": [0, 0], "ix": 2}}, "a": []}, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Frame 1171276473", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [40, 16.607, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"ty": "rc", "d": 1, "s": {"a": 0, "k": [80, 33.214], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 4}, "nm": "矩形路径 1", "mn": "ADBE Vector Shape - Rect", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Frame 1171276473", "np": 1, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}]}], "fonts": {"list": [{"fName": "TencentSansW7", "fFamily": "TencentSans", "fStyle": "W7", "ascent": 79.7985840588808}, {"fName": "DingTalk-JinBuTi", "fFamily": "DingTalk JinBuTi", "fStyle": "Regular", "ascent": 77.098535373807}, {"fName": "PingFangSC-Regular", "fFamily": "PingFang SC", "fStyle": "Regular", "ascent": 72.7996826171875}]}, "layers": [{"ddd": 0, "ind": 1, "ty": 0, "nm": "Frame 1171276477", "refId": "comp_0", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [304, 131.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [58, 16, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 116, "h": 32, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 2, "ty": 0, "nm": "Frame 1171276464", "refId": "comp_3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [212.5, 185, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [46, 15.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 92, "h": 31, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 3, "ty": 3, "nm": "▽ Group 3", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [83.5, 136, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [46, 16, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 4, "ty": 0, "nm": "Frame 1171276461", "parent": 3, "refId": "comp_6", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [46, 20.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [46, 8.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 92, "h": 17, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 5, "ty": 4, "nm": "Ellipse 838", "parent": 3, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [1]}, {"t": 120, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [80, 1, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 60, "s": [60, 60, 100]}, {"t": 120, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [8, 8], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456580519676, 0.631011545658, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 838", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 6, "ty": 0, "nm": "Frame 1171276466", "refId": "comp_8", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [124, 30, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [46, 16, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 92, "h": 32, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 7, "ty": 3, "nm": "▽ Group 4", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [260.5, 17.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [64, 15.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 8, "ty": 0, "nm": "Frame 1171276459", "parent": 7, "refId": "comp_11", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [64, 5.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [64, 8.5, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 128, "h": 17, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 9, "ty": 4, "nm": "Ellipse 836", "parent": 7, "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [0]}, {"t": 120, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [27, 20.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 60, "s": [60, 60, 100]}, {"t": 120, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [8, 8], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "fl", "c": {"a": 0, "k": [0.456580519676, 0.631011545658, 1, 1], "ix": 4}, "o": {"a": 0, "k": 100, "ix": 5}, "r": 1, "bm": 0, "nm": "填充 1", "mn": "ADBE Vector Graphic - Fill", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 836", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 10, "ty": 0, "nm": "Frame 1171276474", "refId": "comp_13", "sr": 1, "ks": {"o": {"a": 0, "k": 100, "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [194.5, 98.5, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [140, 104, 0], "ix": 1, "l": 2}, "s": {"a": 0, "k": [100, 100, 100], "ix": 6, "l": 2}}, "ao": 0, "w": 280, "h": 208, "ip": 0, "op": 300, "st": 0, "bm": 0}, {"ddd": 0, "ind": 11, "ty": 4, "nm": "Ellipse 835", "sr": 1, "ks": {"o": {"a": 1, "k": [{"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 0, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 30, "s": [60]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 60, "s": [100]}, {"i": {"x": [0.833], "y": [0.833]}, "o": {"x": [0.167], "y": [0.167]}, "t": 90, "s": [60]}, {"t": 120, "s": [100]}], "ix": 11}, "r": {"a": 0, "k": 0, "ix": 10}, "p": {"a": 0, "k": [195.5, 97, 0], "ix": 2, "l": 2}, "a": {"a": 0, "k": [0, 0, 0], "ix": 1, "l": 2}, "s": {"a": 1, "k": [{"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 0, "s": [100, 100, 100]}, {"i": {"x": [0.833, 0.833, 0.833], "y": [0.833, 0.833, 0.833]}, "o": {"x": [0.167, 0.167, 0.167], "y": [0.167, 0.167, 0.167]}, "t": 60, "s": [46, 46, 100]}, {"t": 120, "s": [100, 100, 100]}], "ix": 6, "l": 2}}, "ao": 0, "shapes": [{"ty": "gr", "it": [{"d": 1, "ty": "el", "s": {"a": 0, "k": [160, 160], "ix": 2}, "p": {"a": 0, "k": [0, 0], "ix": 3}, "nm": "椭圆路径 1", "mn": "ADBE Vector Shape - Ellipse", "hd": false}, {"ty": "st", "c": {"a": 0, "k": [0.672288179398, 0.777479588985, 1, 1], "ix": 3}, "o": {"a": 0, "k": 100, "ix": 4}, "w": {"a": 0, "k": 1, "ix": 5}, "lc": 2, "lj": 1, "ml": 4, "bm": 0, "d": [{"n": "d", "nm": "虚线", "v": {"a": 0, "k": 4, "ix": 1}}, {"n": "g", "nm": "间隙", "v": {"a": 0, "k": 4, "ix": 2}}], "nm": "描边 1", "mn": "ADBE Vector Graphic - Stroke", "hd": false}, {"ty": "tr", "p": {"a": 0, "k": [0, 0], "ix": 2}, "a": {"a": 0, "k": [0, 0], "ix": 1}, "s": {"a": 0, "k": [100, 100], "ix": 3}, "r": {"a": 0, "k": 0, "ix": 6}, "o": {"a": 0, "k": 100, "ix": 7}, "sk": {"a": 0, "k": 0, "ix": 4}, "sa": {"a": 0, "k": 0, "ix": 5}, "nm": "变换"}], "nm": "Ellipse 835", "np": 2, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}], "ip": 0, "op": 300, "st": 0, "bm": 0}], "markers": [], "chars": [{"ch": "分", "size": 14, "style": "W7", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[3.7, 6.934], [3.133, 6.4], [0, 0], [-6.134, -11.4], [1.266, 0], [0, 0], [-4.6, 6.367], [-3.6, 5.6], [0, 0], [5.5, -6.966], [5.4, -6.066], [0, 0], [0, 0], [0, 0], [3.066, -4.566], [3.066, -3.533], [2.8, -2.667], [1.866, -1.6], [0, 0], [-4.9, 7.1], [-3.734, 8.6], [0, 0], [-0.2, -0.2], [0.066, -0.466], [2.066, -11.533], [0, 0], [0, 0], [0, 0], [-0.867, 5.5], [-0.867, 6.467], [-0.4, 4], [0, 0], [0, 0]], "o": [[-3.7, -6.933], [0, 0], [5.533, 12.534], [-0.467, -0.066], [0, 0], [4.533, -5.533], [4.6, -6.366], [0, 0], [-4.334, 6.2], [-5.5, 6.967], [0, 0], [0, 0], [0, 0], [-2.334, 5.6], [-3.067, 4.567], [-3.067, 3.533], [-2.8, 2.666], [0, 0], [6.466, -5.934], [4.9, -7.1], [0, 0], [0.4, 0], [0.2, 0.2], [-1.134, 10.134], [0, 0], [0, 0], [0, 0], [0.6, -3.467], [0.866, -5.5], [0.866, -6.466], [0, 0], [0, 0], [-3.467, -5.933]], "v": [[85.45, -54.8], [75.2, -74.8], [62.8, -74.8], [80.3, -38.9], [77.7, -39], [23.8, -39], [37.5, -56.85], [49.8, -74.8], [36.3, -74.8], [21.55, -55.05], [5.2, -35.5], [19.1, -35.5], [21.4, -29.9], [36.9, -29.9], [28.8, -14.65], [19.6, -2.5], [10.8, 6.8], [3.8, 13.2], [19.5, 13.2], [36.55, -6.35], [49.5, -29.9], [70, -29.9], [70.9, -29.6], [71.1, -28.6], [66.3, 3.9], [47.6, 3.9], [50.5, 13.2], [76.7, 13.2], [78.9, -0.25], [81.5, -18.2], [83.4, -33.9], [83.5, -35.5], [96.2, -35.5]], "c": true}, "ix": 2}, "nm": "分", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "分", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "TencentSans"}, {"ch": "析", "size": 14, "style": "W7", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[49.3, -60.8], [35.7, -60.8], [38, -76.8], [27.8, -76.8], [25.5, -60.8], [10.8, -60.8], [9.5, -52.4], [24.3, -52.4], [14.8, 13.2], [25.3, 13.2], [34.6, -52.4], [48, -52.4]], "c": true}, "ix": 2}, "nm": "析", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-8.334, 1.467], [0, 0], [8.066, -1.2], [6.8, -0.666], [2.666, -12.966], [4.6, -12.134], [0, 0], [-3, 18.134], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.667, 6.4]], "o": [[0, 0], [-6.8, 1.467], [-8.067, 1.2], [-1.4, 16.8], [-2.667, 12.967], [0, 0], [4.933, -14.266], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.8, -5.2], [13.2, -1.333]], "v": [[96.1, -65.6], [98.3, -75.9], [76, -71.9], [53.7, -69.1], [47.6, -24.45], [36.7, 13.2], [48.5, 13.2], [60.4, -35.4], [73.5, -35.4], [66.3, 13.2], [77.5, 13.2], [84.3, -35.4], [95.8, -35.4], [97, -44], [61.6, -44], [63.8, -61.4]], "c": true}, "ix": 2}, "nm": "析", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[2.266, -8.1], [2.866, -8.333], [0, 0], [-3.2, 15.867], [0, 0]], "o": [[-2.267, 8.1], [0, 0], [5.2, -16.333], [0, 0], [-1.534, 7.667]], "v": [[6.6, -20.95], [-1.1, 3.7], [8.2, 3.7], [20.8, -44.6], [12.3, -44.6]], "c": true}, "ix": 2}, "nm": "析", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [1.066, -9.566], [0.333, -3.533], [0, 0], [-1.867, 20]], "o": [[-0.4, 4.934], [-1.067, 9.567], [0, 0], [0.533, -7.6], [0, 0]], "v": [[36.2, -44.6], [34, -22.85], [31.9, -3.2], [40.6, -3.2], [44.2, -44.6]], "c": true}, "ix": 2}, "nm": "析", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "析", "np": 7, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "TencentSans"}, {"ch": "助", "size": 14, "style": "W7", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [-0.2, 3.4], [0, 0], [0.866, -7.333], [0, 0], [0, 0], [0, 0], [1.666, -6.5], [2.333, -5.5], [3.2, -5.934], [0, 0], [-1.434, 3.233], [-1.334, 3.8], [-1.167, 4.5], [-1.034, 5.5], [-0.867, 6.4], [0, 0], [-0.034, -0.333], [0, -0.2], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.833, 0.934], [2.333, 0]], "o": [[0.8, -8], [0, 0], [-0.067, 4.067], [0, 0], [0, 0], [0, 0], [-1.734, 12.867], [-1.667, 6.5], [-2.334, 5.5], [0, 0], [1.2, -2.334], [1.433, -3.234], [1.333, -3.8], [1.166, -4.5], [1.033, -5.5], [0, 0], [0.8, 0], [0.033, 0.334], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.266, -2.6], [-0.834, -0.933], [0, 0]], "v": [[75.5, -59.7], [77, -76.8], [66.2, -76.8], [64.8, -59.7], [52.2, -59.7], [54.6, -51], [63.8, -51], [58.7, -21.95], [52.7, -3.95], [44.4, 13.2], [56.5, 13.2], [60.45, 4.85], [64.6, -5.7], [68.35, -18.15], [71.65, -33.15], [74.5, -51], [82.6, -51], [83.85, -50.5], [83.9, -49.7], [76.6, 4.6], [66.7, 4.6], [69.4, 13.2], [86.4, 13.2], [95.1, -53], [94.25, -58.3], [89.5, -59.7]], "c": true}, "ix": 2}, "nm": "助", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0.7, 0.867], [2.333, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-13.867, 3.8], [0, 0]], "o": [[0, 0], [0.333, -2.4], [-0.7, -0.866], [0, 0], [0, 0], [0, 0], [0, 0], [18.866, -3.734], [0, 0], [0, 0]], "v": [[43.6, -6.6], [52.1, -67.4], [51.55, -72.3], [47, -73.6], [17.9, -73.6], [7.1, 1.9], [0.7, 3.2], [-0.4, 12.5], [48.7, 1.2], [49.9, -8.4]], "c": true}, "ix": 2}, "nm": "助", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0.066, -0.6], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0.466, 0]], "v": [[41.2, -64.3], [39.2, -50.2], [25, -50.2], [27.1, -65.2], [40.6, -65.2]], "c": true}, "ix": 2}, "nm": "助", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[21.7, -26.5], [23.8, -41.8], [38, -41.8], [35.8, -26.5]], "c": true}, "ix": 2}, "nm": "助", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[34.6, -18.1], [32.6, -3.8], [18, -0.4], [20.5, -18.1]], "c": true}, "ix": 2}, "nm": "助", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "助", "np": 8, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "TencentSans"}, {"ch": "手", "size": 14, "style": "W7", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-10.134, 1.534], [0, 0], [9.7, -1.2], [8.433, -0.633], [6.2, -0.266], [0, 0], [-11.4, 1.134], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [10.4, -1.133], [0, 0], [-9.067, 1.467], [-9.7, 1.2], [-8.434, 0.634], [0, 0], [11.533, -0.6], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[55, -24], [56.7, -37.2], [89.6, -37.2], [91, -47], [58, -47], [60, -62.5], [90.8, -66.5], [92.1, -76.7], [63.95, -72.7], [36.75, -69.95], [14.8, -68.6], [13.5, -58.6], [47.9, -61.2], [46, -47], [12.3, -47], [10.9, -37.2], [44.7, -37.2], [42.9, -24], [4.4, -24], [3, -14.2], [41.6, -14.2], [39.2, 3.2], [24.4, 3.2], [27, 13.2], [50.1, 13.2], [53.7, -14.2], [92.5, -14.2], [93.9, -24]], "c": true}, "ix": 2}, "nm": "手", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "手", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "TencentSans"}, {"ch": "A", "size": 14, "style": "Regular", "w": 72.7, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [1.266, -3.066], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [3.733, 0]], "o": [[-3.4, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.6, -3.6], [0, 0]], "v": [[33.7, -71.5], [26.7, -66.9], [-2.1, 3.6], [10.8, 3.6], [19.5, -18.1], [51.8, -18.1], [55.1, 3.6], [67.6, 3.6], [56.2, -66.1], [49.7, -71.5]], "c": true}, "ix": 2}, "nm": "A", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[45.3, -60.5], [50.1, -29.1], [23.9, -29.1], [36.6, -60.5]], "c": true}, "ix": 2}, "nm": "A", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "A", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "DingTalk JinBuTi"}, {"ch": "I", "size": 14, "style": "Regular", "w": 26.6, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[3.8, 3.6], [15.6, 3.6], [24.8, -71.5], [13, -71.5]], "c": true}, "ix": 2}, "nm": "I", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "I", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "DingTalk JinBuTi"}, {"ch": "经", "size": 12, "style": "Regular", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[63.144, -22.659], [63.144, -0.403], [38.873, -0.403], [38.873, 6.647], [95.068, 6.647], [95.068, -0.403], [70.596, -0.403], [70.596, -22.659], [90.436, -22.659], [90.436, -29.709], [43.405, -29.709], [43.405, -22.659]], "c": true}, "ix": 2}, "nm": "经", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-7.352, 6.445], [-5.237, -5.338], [0, 0], [8.459, 5.338], [-2.618, 6.042], [0, 0], [0, 0], [0, 0], [0, 0], [18.832, -7.05], [0, 0]], "o": [[8.762, 5.841], [0, 0], [-5.841, -5.338], [4.633, -4.935], [0, 0], [0, 0], [0, 0], [0, 0], [-7.251, 12.891], [0, 0], [11.078, -4.33]], "v": [[70.093, -51.059], [91.04, -34.241], [96.378, -39.578], [74.826, -55.591], [85.703, -72.006], [85.703, -77.747], [42.599, -77.747], [42.599, -70.798], [77.042, -70.798], [37.866, -40.787], [42.398, -34.845]], "c": true}, "ix": 2}, "nm": "经", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [12.589, -2.921], [0, 0], [-9.467, 4.834]], "o": [[-9.467, 4.834], [0, 0], [11.884, -3.021], [0, 0]], "v": [[37.866, -12.286], [4.733, -0.604], [5.74, 6.345], [37.866, -5.438]], "c": true}, "ix": 2}, "nm": "经", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [5.338, -9.064], [1.108, -0.504], [0, 0], [-5.74, 0.906], [4.028, -4.028], [1.208, -0.504], [0, 0], [-10.373, 3.625], [0, 0], [7.05, -1.511], [-9.265, 17.02], [0, 0], [1.913, -3.525], [4.633, -0.403], [-5.035, 12.891]], "o": [[-3.928, 11.682], [-0.906, 1.309], [0, 0], [5.74, -0.806], [-5.64, 9.668], [-0.906, 0.806], [0, 0], [9.567, -1.309], [0, 0], [-7.05, 2.518], [6.143, -7.251], [0, 0], [-2.014, 3.928], [-4.733, 0.806], [5.136, -7.654], [0, 0]], "v": [[21.854, -82.379], [7.956, -51.16], [4.935, -48.441], [6.747, -41.794], [23.969, -44.312], [9.467, -23.767], [6.244, -21.753], [8.157, -15.106], [38.068, -22.458], [38.068, -29.105], [17.02, -23.062], [40.182, -59.418], [33.536, -61.935], [27.594, -50.757], [13.596, -48.944], [28.802, -79.66]], "c": true}, "ix": 2}, "nm": "经", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "经", "np": 7, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "PingFang SC"}, {"ch": "营", "size": 12, "style": "Regular", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[25.479, -45.52], [25.479, -25.076], [75.632, -25.076], [75.632, -45.52]], "c": true}, "ix": 2}, "nm": "营", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[68.884, -30.615], [32.227, -30.615], [32.227, -39.88], [68.884, -39.88]], "c": true}, "ix": 2}, "nm": "营", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[85.199, -18.228], [16.516, -18.228], [16.516, 10.172], [23.566, 10.172], [23.566, 6.042], [78.25, 6.042], [78.25, 10.172], [85.199, 10.172]], "c": true}, "ix": 2}, "nm": "营", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[23.566, -0.101], [23.566, -12.186], [78.25, -12.186], [78.25, -0.101]], "c": true}, "ix": 2}, "nm": "营", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[7.05, -75.33], [7.05, -68.985], [29.709, -68.985], [29.709, -63.043], [36.96, -63.043], [36.96, -68.985], [63.748, -68.985], [63.748, -63.043], [70.999, -63.043], [70.999, -68.985], [93.658, -68.985], [93.658, -75.33], [70.999, -75.33], [70.999, -82.077], [63.748, -82.077], [63.748, -75.33], [36.96, -75.33], [36.96, -82.077], [29.709, -82.077], [29.709, -75.33]], "c": true}, "ix": 2}, "nm": "营", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[90.839, -58.813], [9.869, -58.813], [9.869, -39.478], [16.818, -39.478], [16.818, -52.67], [83.89, -52.67], [83.89, -39.981], [90.839, -39.981]], "c": true}, "ix": 2}, "nm": "营", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "营", "np": 9, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "PingFang SC"}, {"ch": "基", "size": 12, "style": "Regular", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [9.97, -3.827], [0, 0], [-5.237, 8.359], [0, 0], [-12.488, -5.237], [0, 0], [4.834, 6.042], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-4.834, 5.942], [0, 0], [11.682, -5.438], [0, 0], [5.237, 8.359], [0, 0], [-10.071, -3.424], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[8.057, -74.725], [8.057, -68.582], [25.882, -68.582], [25.882, -35.248], [6.546, -35.248], [6.546, -28.802], [27.493, -28.802], [5.338, -14.2], [9.064, -8.057], [34.442, -28.802], [66.064, -28.802], [92.651, -8.359], [95.572, -14.603], [73.114, -28.802], [94.061, -28.802], [94.061, -35.248], [74.725, -35.248], [74.725, -68.582], [92.551, -68.582], [92.551, -74.725], [74.725, -74.725], [74.725, -82.379], [67.676, -82.379], [67.676, -74.725], [32.932, -74.725], [32.932, -82.379], [25.882, -82.379], [25.882, -74.725]], "c": true}, "ix": 2}, "nm": "基", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[32.932, -35.248], [32.932, -42.499], [67.676, -42.499], [67.676, -35.248]], "c": true}, "ix": 2}, "nm": "基", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[32.932, -48.239], [32.932, -55.389], [67.676, -55.389], [67.676, -48.239]], "c": true}, "ix": 2}, "nm": "基", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[32.932, -61.13], [32.932, -68.582], [67.676, -68.582], [67.676, -61.13]], "c": true}, "ix": 2}, "nm": "基", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[46.829, -25.278], [46.829, -17.221], [26.184, -17.221], [26.184, -10.776], [46.829, -10.776], [46.829, -0.101], [11.581, -0.101], [11.581, 6.647], [88.422, 6.647], [88.422, -0.101], [53.879, -0.101], [53.879, -10.776], [74.323, -10.776], [74.323, -17.221], [53.879, -17.221], [53.879, -25.278]], "c": true}, "ix": 2}, "nm": "基", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "基", "np": 8, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "PingFang SC"}, {"ch": "本", "size": 12, "style": "Regular", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [16.919, -11.279], [0, 0], [-7.452, 20.444], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-17.725, -14.502], [0, 0], [6.747, 16.013], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-7.452, 17.825], [0, 0], [17.926, -13.193], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [6.848, 18.228], [0, 0], [-17.322, -12.589], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[7.251, -63.748], [7.251, -56.799], [40.182, -56.799], [3.726, -13.092], [7.855, -6.345], [45.923, -56.799], [46.628, -56.799], [46.628, -17.523], [25.882, -17.523], [25.882, -10.574], [46.628, -10.574], [46.628, 10.071], [54.08, 10.071], [54.08, -10.574], [75.632, -10.574], [75.632, -17.523], [54.08, -17.523], [54.08, -56.799], [54.886, -56.799], [91.745, -7.654], [96.68, -13.898], [60.526, -56.799], [93.356, -56.799], [93.356, -63.748], [54.08, -63.748], [54.08, -81.976], [46.628, -81.976], [46.628, -63.748]], "c": true}, "ix": 2}, "nm": "本", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "本", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "PingFang SC"}, {"ch": "面", "size": 12, "style": "Regular", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [1.611, -4.23], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.806, 4.431], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.806, 4.431], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [1.611, -4.129], [0, 0], [0, 0], [0, 0]], "v": [[5.539, -78.25], [5.539, -71.201], [47.433, -71.201], [43.707, -58.31], [11.279, -58.31], [11.279, 10.373], [18.228, 10.373], [18.228, 5.338], [82.782, 5.338], [82.782, 10.373], [89.731, 10.373], [89.731, -58.31], [51.059, -58.31], [54.785, -71.201], [95.169, -71.201], [95.169, -78.25]], "c": true}, "ix": 2}, "nm": "面", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[18.228, -1.309], [18.228, -51.764], [32.126, -51.764], [32.126, -1.309]], "c": true}, "ix": 2}, "nm": "面", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[38.873, -1.309], [38.873, -14.2], [61.835, -14.2], [61.835, -1.309]], "c": true}, "ix": 2}, "nm": "面", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[68.582, -1.309], [68.582, -51.764], [82.782, -51.764], [82.782, -1.309]], "c": true}, "ix": 2}, "nm": "面", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[38.873, -20.544], [38.873, -32.831], [61.835, -32.831], [61.835, -20.544]], "c": true}, "ix": 2}, "nm": "面", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[38.873, -39.276], [38.873, -51.764], [61.835, -51.764], [61.835, -39.276]], "c": true}, "ix": 2}, "nm": "面", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "面", "np": 9, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "PingFang SC"}, {"ch": "专", "size": 12, "style": "Regular", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [2.417, -6.445], [0, 0], [9.265, -6.042], [7.553, 2.014], [0, 0], [-15.106, -7.452], [0, 0], [6.042, 2.216], [-4.834, 7.452], [0, 0], [0, 0], [-1.41, 4.733], [0, 0], [0, 0], [0, 0], [-1.007, 4.431], [0, 0], [0, 0], [0, 0], [-0.806, 4.028], [0, 0], [0.906, -4.129], [0, 0], [0, 0], [0, 0], [1.108, -4.129]], "o": [[0, 0], [0, 0], [-2.115, 6.949], [0, 0], [-5.338, 6.647], [-6.949, -2.417], [0, 0], [16.013, 4.33], [0, 0], [-5.539, -2.618], [10.373, -7.452], [0, 0], [0, 0], [1.41, -4.129], [0, 0], [0, 0], [0, 0], [1.007, -3.928], [0, 0], [0, 0], [0, 0], [0.806, -3.625], [0, 0], [-0.806, 4.028], [0, 0], [0, 0], [0, 0], [-1.007, 4.23], [0, 0]], "v": [[5.136, -51.562], [5.136, -44.513], [31.924, -44.513], [25.076, -24.472], [74.826, -24.472], [52.872, -5.338], [31.119, -12.085], [27.292, -6.445], [74.02, 11.179], [78.049, 4.834], [60.626, -2.518], [83.386, -24.976], [83.386, -31.219], [35.449, -31.219], [39.78, -44.513], [95.572, -44.513], [95.572, -51.562], [41.693, -51.562], [44.714, -64.05], [89.63, -64.05], [89.63, -70.798], [46.225, -70.798], [48.541, -82.379], [41.089, -82.983], [38.571, -70.798], [11.984, -70.798], [11.984, -64.05], [37.061, -64.05], [33.939, -51.562]], "c": true}, "ix": 2}, "nm": "专", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "专", "np": 3, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "PingFang SC"}, {"ch": "项", "size": 12, "style": "Regular", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [4.532, -1.108], [0, 0], [-8.862, 3.726], [0, 0], [4.33, -1.511], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-4.23, 1.208], [0, 0], [11.682, -3.122], [0, 0], [-3.928, 1.611], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[17.523, -65.359], [17.523, -17.523], [4.431, -14.099], [6.546, -6.949], [37.363, -17.221], [37.363, -24.472], [24.976, -19.839], [24.976, -65.359], [36.658, -65.359], [36.658, -72.409], [5.539, -72.409], [5.539, -65.359]], "c": true}, "ix": 2}, "nm": "项", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [1.007, -3.424], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-0.604, 3.827], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-0.504, 3.726], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0.806, -3.424], [0, 0], [0, 0], [0, 0]], "v": [[40.485, -77.948], [40.485, -70.898], [65.057, -70.898], [62.842, -60.123], [45.117, -60.123], [45.117, -12.79], [52.066, -12.79], [52.066, -53.476], [84.393, -53.476], [84.393, -12.79], [91.443, -12.79], [91.443, -60.123], [70.093, -60.123], [72.208, -70.898], [96.277, -70.898], [96.277, -77.948]], "c": true}, "ix": 2}, "nm": "项", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[8.258, 6.042], [0, 0], [-5.035, -5.841], [0, 0]], "o": [[0, 0], [8.661, 6.647], [0, 0], [-5.64, -6.042]], "v": [[76.437, -12.589], [71.603, -7.956], [92.148, 10.776], [97.385, 5.539]], "c": true}, "ix": 2}, "nm": "项", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [4.23, -5.438], [11.682, -3.122], [0, 0], [-5.338, 5.942], [-0.403, 10.876], [0, 0]], "o": [[0, 0], [-0.302, 8.762], [-4.633, 5.237], [0, 0], [12.085, -3.424], [5.438, -6.546], [0, 0], [0, 0]], "v": [[64.151, -47.031], [64.151, -29.709], [57.303, -8.359], [32.73, 4.23], [36.658, 10.474], [62.741, -3.625], [71.402, -29.709], [71.402, -47.031]], "c": true}, "ix": 2}, "nm": "项", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "项", "np": 7, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "PingFang SC"}, {"ch": "检", "size": 12, "style": "Regular", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [7.05, -10.272], [0, 0], [-3.223, 10.071], [0, 0], [0, 0], [0, 0], [-3.223, -5.74], [0, 0], [4.028, 4.431], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-3.021, 13.193], [0, 0], [5.035, -8.258], [0, 0], [0, 0], [0, 0], [2.316, 3.625], [0, 0], [-4.23, -5.64], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[6.042, -62.54], [6.042, -55.591], [18.43, -55.591], [3.223, -20.444], [6.345, -12.488], [18.631, -39.88], [18.631, 9.567], [25.781, 9.567], [25.781, -43.304], [34.039, -29.205], [38.168, -35.349], [25.781, -50.455], [25.781, -55.591], [36.154, -55.591], [36.154, -62.54], [25.781, -62.54], [25.781, -82.178], [18.631, -82.178], [18.631, -62.54]], "c": true}, "ix": 2}, "nm": "检", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-3.827, 4.028], [0, 0], [0, 0], [0, 0], [-4.33, -3.122], [0, 0], [5.438, 12.689], [0, 0], [11.884, -8.56], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [3.424, 3.525], [0, 0], [-13.193, -9.567], [0, 0], [-6.445, 13.797], [0, 0], [4.23, -3.021]], "v": [[50.958, -52.872], [50.958, -46.426], [81.573, -46.426], [81.573, -52.57], [93.256, -42.599], [97.183, -48.743], [69.287, -82.077], [62.64, -82.077], [35.046, -48.541], [38.974, -42.398]], "c": true}, "ix": 2}, "nm": "检", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[-6.345, -6.546], [0, 0], [-4.431, 8.56]], "o": [[0, 0], [5.438, -6.042], [3.726, 8.057]], "v": [[81.07, -53.073], [51.16, -53.073], [65.964, -74.927]], "c": true}, "ix": 2}, "nm": "检", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-2.82, 14.2], [0, 0], [4.129, -10.776]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [4.23, -11.078], [0, 0], [-3.122, 15.71], [0, 0]], "v": [[37.363, 0], [37.363, 7.05], [95.068, 7.05], [95.068, 0], [79.358, 0], [90.033, -37.866], [83.084, -39.679], [72.208, 0]], "c": true}, "ix": 2}, "nm": "检", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[2.82, 8.459], [0, 0], [-2.014, -10.071], [0, 0]], "o": [[0, 0], [2.82, 9.265], [0, 0], [-2.115, -11.078]], "v": [[48.642, -36.96], [42.398, -34.845], [49.649, -5.942], [55.994, -7.654]], "c": true}, "ix": 2}, "nm": "检", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[2.618, 8.157], [0, 0], [-1.712, -9.668], [0, 0]], "o": [[0, 0], [2.618, 8.862], [0, 0], [-2.014, -10.574]], "v": [[64.151, -40.585], [57.907, -38.571], [64.453, -10.776], [71.1, -12.488]], "c": true}, "ix": 2}, "nm": "检", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "检", "np": 9, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "PingFang SC"}, {"ch": "查", "size": 12, "style": "Regular", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[20.444, -44.513], [20.444, -7.452], [80.667, -7.452], [80.667, -44.513]], "c": true}, "ix": 2}, "nm": "查", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[73.618, -13.898], [27.493, -13.898], [27.493, -22.76], [73.618, -22.76]], "c": true}, "ix": 2}, "nm": "查", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[27.493, -28.903], [27.493, -38.068], [73.618, -38.068], [73.618, -28.903]], "c": true}, "ix": 2}, "nm": "查", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [16.113, -4.633], [0, 0], [-7.855, 9.668], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-18.127, -6.143], [0, 0], [7.553, 7.352], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [-7.855, 7.05], [0, 0], [17.624, -6.345], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [7.452, 9.869], [0, 0], [-15.912, -4.23], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[9.164, -73.215], [9.164, -66.568], [40.283, -66.568], [4.33, -48.944], [8.157, -42.499], [46.426, -66.568], [47.031, -66.568], [47.031, -47.736], [54.08, -47.736], [54.08, -66.568], [54.684, -66.568], [93.054, -42.599], [96.176, -49.146], [61.029, -66.568], [91.946, -66.568], [91.946, -73.215], [54.08, -73.215], [54.08, -82.48], [47.031, -82.48], [47.031, -73.215]], "c": true}, "ix": 2}, "nm": "查", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[7.15, 0.403], [7.15, 7.15], [93.658, 7.15], [93.658, 0.403]], "c": true}, "ix": 2}, "nm": "查", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "查", "np": 8, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "PingFang SC"}, {"ch": "财", "size": 12, "style": "Regular", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [13.092, -11.682], [0, 0], [-5.942, 17.725], [0, 0], [2.417, 0], [4.129, 0.302], [0, 0], [0, 0], [0, 6.345], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[-5.841, 18.53], [0, 0], [12.488, -11.884], [0, 0], [0, 3.223], [-3.827, 0], [0, 0], [0, 0], [5.438, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[74.02, -55.49], [45.621, -10.172], [49.548, -3.726], [77.142, -48.038], [77.142, -2.115], [73.416, 2.82], [61.432, 2.316], [62.943, 9.265], [75.934, 9.265], [84.192, -0.302], [84.192, -55.49], [95.773, -55.49], [95.773, -62.238], [84.192, -62.238], [84.192, -82.077], [77.142, -82.077], [77.142, -62.238], [50.354, -62.238], [50.354, -55.49]], "c": true}, "ix": 2}, "nm": "财", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[44.614, -78.25], [8.963, -78.25], [8.963, -14.603], [15.61, -14.603], [15.61, -71.503], [38.168, -71.503], [38.168, -15.408], [44.614, -15.408]], "c": true}, "ix": 2}, "nm": "财", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [2.82, -5.841], [7.553, -3.424], [0, 0], [-3.525, 6.345], [-0.201, 11.481], [0, 0]], "o": [[0, 0], [-0.201, 9.366], [-3.021, 5.64], [0, 0], [7.855, -3.625], [3.525, -6.848], [0, 0], [0, 0]], "v": [[23.868, -63.547], [23.868, -32.327], [19.437, -9.567], [3.525, 3.928], [7.452, 10.071], [24.472, -4.834], [30.112, -32.327], [30.112, -63.547]], "c": true}, "ix": 2}, "nm": "财", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[5.64, 5.136], [0, 0], [-3.323, -5.035], [0, 0]], "o": [[0, 0], [5.74, 5.64], [0, 0], [-3.827, -5.035]], "v": [[33.939, -11.884], [29.407, -7.452], [43.002, 8.459], [48.038, 3.424]], "c": true}, "ix": 2}, "nm": "财", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "财", "np": 7, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "PingFang SC"}, {"ch": "务", "size": 12, "style": "Regular", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [12.085, -6.949], [0, 0], [-3.827, 3.424], [-5.64, -3.323], [14.603, -2.014], [0, 0], [-12.286, 5.64], [-15.912, -1.813], [0, 0], [11.38, 4.834], [-6.445, 7.452], [0, 0], [0, 0], [-1.108, 2.719]], "o": [[-4.23, 10.071], [0, 0], [4.633, -2.82], [4.834, 4.23], [-11.078, 4.431], [0, 0], [16.315, -2.518], [12.488, 6.244], [0, 0], [-13.998, -1.41], [9.265, -5.237], [0, 0], [0, 0], [1.41, -2.417], [0, 0]], "v": [[34.644, -82.581], [10.071, -57.101], [14.502, -51.26], [27.09, -60.626], [42.801, -49.246], [4.33, -39.578], [7.654, -32.73], [50.656, -45.016], [93.256, -32.932], [96.579, -39.679], [58.511, -49.045], [82.077, -68.079], [82.077, -73.92], [38.168, -73.92], [41.995, -81.573]], "c": true}, "ix": 2}, "nm": "务", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[8.157, -4.028], [5.64, 4.935], [0, 0], [0, 0]], "o": [[-6.747, -3.525], [0, 0], [0, 0], [-5.942, 5.438]], "v": [[50.555, -52.771], [31.924, -65.46], [33.234, -67.072], [71.704, -67.072]], "c": true}, "ix": 2}, "nm": "务", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0.806, -3.625], [0, 0], [0, 0], [0, 0], [1.208, -1.813], [16.516, -4.834], [0, 0], [-5.136, 9.064], [-0.806, 2.618], [0, 0], [1.511, -2.417], [6.042, -0.101], [0, 0], [0, 0], [-3.223, 0], [-2.921, 3.323], [-0.201, 17.02], [0, 0], [0, 4.028]], "o": [[0, 4.028], [0, 0], [0, 0], [0, 0], [-0.806, 2.014], [-5.237, 7.352], [0, 0], [18.53, -5.841], [1.309, -2.417], [0, 0], [-0.302, 10.071], [-1.611, 2.417], [0, 0], [0, 0], [4.633, 0.201], [7.15, 0], [2.921, -3.323], [0, 0], [0.604, -3.625], [0, 0]], "v": [[44.412, -37.766], [43.204, -26.285], [11.179, -26.285], [11.179, -19.739], [41.19, -19.739], [38.068, -13.998], [5.338, 4.33], [10.172, 10.272], [45.621, -12.186], [48.843, -19.739], [78.653, -19.739], [75.934, -1.007], [64.554, 2.82], [52.67, 2.316], [54.483, 8.862], [66.266, 9.265], [81.372, 4.23], [86.005, -26.285], [50.555, -26.285], [51.562, -37.766]], "c": true}, "ix": 2}, "nm": "务", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "务", "np": 6, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "PingFang SC"}, {"ch": "税", "size": 12, "style": "Regular", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [9.668, -6.647], [0, 0], [-0.806, 18.027], [0, 0], [0, 0], [-4.935, 0], [0, 0], [-1.41, 1.813], [-0.604, 9.467], [0, 0], [0.806, -1.511], [1.41, 0], [0, 0], [0, 2.417], [0, 0], [0, 0], [0, 0], [0, 0], [-2.719, 7.452], [0, 0], [3.726, -6.747]], "o": [[0, 0], [0, 0], [-0.806, 15.308], [0, 0], [11.581, -8.157], [0, 0], [0, 0], [0, 5.841], [0, 0], [3.021, 0], [1.611, -2.115], [0, 0], [-0.302, 7.553], [-0.604, 1.208], [0, 0], [-1.913, 0], [0, 0], [0, 0], [0, 0], [0, 0], [3.424, -5.64], [0, 0], [-2.82, 7.956], [0, 0]], "v": [[46.426, -60.626], [46.426, -28.702], [55.692, -28.702], [39.981, 4.23], [43.808, 10.574], [62.338, -28.702], [71.603, -28.702], [71.603, -0.101], [78.955, 8.762], [86.71, 8.762], [93.256, 6.042], [96.478, -11.38], [90.134, -13.394], [88.522, 0.201], [85.501, 2.115], [81.07, 2.115], [78.25, -1.611], [78.25, -28.702], [89.026, -28.702], [89.026, -60.626], [77.444, -60.626], [86.609, -80.264], [79.761, -82.681], [69.992, -60.626]], "c": true}, "ix": 2}, "nm": "税", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[82.379, -35.147], [53.174, -35.147], [53.174, -54.181], [82.379, -54.181]], "c": true}, "ix": 2}, "nm": "税", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[3.122, 5.237], [0, 0], [-2.82, -6.546], [0, 0]], "o": [[0, 0], [3.223, 5.136], [0, 0], [-2.921, -6.244]], "v": [[54.785, -81.372], [48.843, -78.552], [57.806, -61.029], [63.849, -64.05]], "c": true}, "ix": 2}, "nm": "税", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [7.654, -9.064], [0, 0], [-3.525, 9.467], [0, 0], [0, 0], [0, 0], [-4.028, -6.647], [0, 0], [4.834, 5.035], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-4.129, 1.208], [0, 0], [12.186, -1.208], [0, 0], [-4.431, 0.705], [0, 0]], "o": [[0, 0], [0, 0], [-3.323, 11.481], [0, 0], [5.64, -7.755], [0, 0], [0, 0], [0, 0], [3.021, 4.028], [0, 0], [-4.935, -6.345], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [4.532, -0.906], [0, 0], [-9.467, 3.021], [0, 0], [4.633, -0.504], [0, 0], [0, 0]], "v": [[6.747, -54.987], [6.747, -48.038], [20.746, -48.038], [4.23, -17.322], [7.452, -8.963], [21.249, -34.744], [21.249, 9.366], [28.299, 9.366], [28.299, -34.341], [38.873, -18.43], [42.902, -24.573], [28.299, -41.693], [28.299, -48.038], [40.686, -48.038], [40.686, -54.987], [28.299, -54.987], [28.299, -70.999], [41.29, -74.121], [38.773, -80.869], [6.244, -74.625], [7.654, -67.978], [21.249, -69.791], [21.249, -54.987]], "c": true}, "ix": 2}, "nm": "税", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "税", "np": 7, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "PingFang SC"}, {"ch": "发", "size": 12, "style": "Regular", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [14.703, -12.891], [0, 0], [-7.352, 15.912], [-7.855, -6.042], [11.279, -4.129], [0, 0], [-8.258, 5.841], [-12.891, -4.733], [0, 0], [8.157, 4.834], [-2.719, 8.661], [0, 0], [0, 0], [-1.208, 4.028], [0, 0], [0, 0], [0, 0], [-1.309, 8.459], [0, 0], [2.014, -7.855], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [-7.05, 22.458], [0, 0], [11.481, -9.366], [3.323, 7.05], [-7.553, 4.935], [0, 0], [12.186, -4.733], [8.459, 5.539], [0, 0], [-11.682, -4.028], [8.258, -7.05], [0, 0], [0, 0], [1.41, -3.625], [0, 0], [0, 0], [0, 0], [1.813, -7.452], [0, 0], [-1.208, 8.661], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[11.682, -51.361], [36.658, -51.361], [4.129, 1.611], [7.855, 8.258], [36.154, -29.608], [52.872, -9.97], [24.673, 3.625], [28.5, 10.373], [59.216, -5.438], [91.241, 9.97], [94.968, 3.223], [65.158, -10.071], [81.573, -33.636], [81.573, -39.88], [40.384, -39.88], [44.211, -51.361], [93.256, -51.361], [93.256, -58.31], [46.124, -58.31], [50.858, -82.077], [43.506, -82.983], [38.672, -58.31], [21.048, -58.31], [26.788, -78.149], [19.638, -79.358]], "c": true}, "ix": 2}, "nm": "发", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[3.424, 6.848], [0, 0], [7.452, -5.841]], "o": [[0, 0], [-2.82, 6.848], [-8.057, -5.841]], "v": [[41.592, -33.234], [74.222, -33.234], [58.813, -14.2]], "c": true}, "ix": 2}, "nm": "发", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[7.251, 5.539], [0, 0], [-4.23, -5.338], [0, 0]], "o": [[0, 0], [7.553, 6.042], [0, 0], [-4.834, -5.539]], "v": [[67.776, -82.178], [62.943, -77.444], [80.667, -60.425], [85.803, -65.561]], "c": true}, "ix": 2}, "nm": "发", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "发", "np": 6, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "PingFang SC"}, {"ch": "票", "size": 12, "style": "Regular", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[5.64, -78.351], [5.64, -72.006], [36.053, -72.006], [36.053, -63.95], [12.991, -63.95], [12.991, -41.592], [87.817, -41.592], [87.817, -63.95], [64.755, -63.95], [64.755, -72.006], [95.068, -72.006], [95.068, -78.351]], "c": true}, "ix": 2}, "nm": "票", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[57.907, -63.95], [42.902, -63.95], [42.902, -72.006], [57.907, -72.006]], "c": true}, "ix": 2}, "nm": "票", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[80.869, -47.534], [64.755, -47.534], [64.755, -57.907], [80.869, -57.907]], "c": true}, "ix": 2}, "nm": "票", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[57.907, -47.534], [42.902, -47.534], [42.902, -57.907], [57.907, -57.907]], "c": true}, "ix": 2}, "nm": "票", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[36.053, -47.534], [19.94, -47.534], [19.94, -57.907], [36.053, -57.907]], "c": true}, "ix": 2}, "nm": "票", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[17.221, -34.14], [17.221, -28.098], [83.99, -28.098], [83.99, -34.14]], "c": true}, "ix": 2}, "nm": "票", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 6, "ty": "sh", "ix": 7, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [2.82, 0], [2.82, 0.201], [0, 0], [0, 0], [0, 5.539], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 2.618], [-2.618, 0], [0, 0], [0, 0], [6.042, 0], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[7.251, -20.746], [7.251, -14.401], [47.937, -14.401], [47.937, -0.302], [43.607, 3.625], [35.449, 3.223], [36.859, 9.668], [45.923, 9.668], [54.987, 1.309], [54.987, -14.401], [93.457, -14.401], [93.457, -20.746]], "c": true}, "ix": 2}, "nm": "票", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 7, "ty": "sh", "ix": 8, "ks": {"a": 0, "k": {"i": [[9.265, 4.935], [0, 0], [-5.539, -4.834], [0, 0]], "o": [[0, 0], [9.869, 5.438], [0, 0], [-6.244, -4.935]], "v": [[70.999, -10.977], [66.165, -6.445], [89.227, 8.862], [94.263, 3.827]], "c": true}, "ix": 2}, "nm": "票", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 8, "ty": "sh", "ix": 9, "ks": {"a": 0, "k": {"i": [[0, 0], [9.668, -4.028], [0, 0], [-6.747, 5.841]], "o": [[-6.647, 5.237], [0, 0], [9.869, -4.33], [0, 0]], "v": [[31.723, -10.776], [7.251, 3.122], [11.581, 8.762], [36.456, -6.445]], "c": true}, "ix": 2}, "nm": "票", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "票", "np": 12, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "PingFang SC"}, {"ch": "关", "size": 12, "style": "Regular", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[4.028, 5.237], [0, 0], [-3.625, -6.445], [0, 0]], "o": [[0, 0], [4.129, 5.035], [0, 0], [-3.625, -6.244]], "v": [[31.219, -81.876], [24.573, -78.653], [36.154, -61.432], [42.7, -64.755]], "c": true}, "ix": 2}, "nm": "关", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [3.827, -5.841], [0, 0], [0, 0], [0, 0], [0, 0], [0.906, -4.431], [0, 0], [0, 0], [0, 0], [2.417, -3.625], [15.408, -4.935], [0, 0], [-6.848, 8.862], [-1.913, 5.74], [-22.156, -8.963], [0, 0], [4.935, 13.596], [0, 0], [0, 0], [0, 0], [-0.201, 5.035], [0, 0], [0, 0], [0, 0], [0, 0], [-2.417, 7.251]], "o": [[-3.021, 8.459], [0, 0], [0, 0], [0, 0], [0, 0], [-0.101, 4.935], [0, 0], [0, 0], [0, 0], [-1.41, 4.431], [-6.042, 8.057], [0, 0], [15.509, -4.935], [3.223, -4.33], [5.438, 15.308], [0, 0], [-21.149, -7.553], [0, 0], [0, 0], [0, 0], [0.806, -4.23], [0, 0], [0, 0], [0, 0], [0, 0], [3.625, -5.74], [0, 0]], "v": [[69.086, -82.782], [58.914, -61.331], [12.891, -61.331], [12.891, -54.282], [46.628, -54.282], [46.628, -49.548], [45.117, -35.55], [7.15, -35.55], [7.15, -28.5], [43.304, -28.5], [37.564, -16.516], [5.338, 3.021], [9.265, 9.366], [42.801, -11.38], [50.455, -26.486], [91.745, 9.97], [95.975, 3.223], [56.9, -28.5], [93.558, -28.5], [93.558, -35.55], [52.67, -35.55], [54.08, -49.548], [54.08, -54.282], [87.717, -54.282], [87.717, -61.331], [66.971, -61.331], [76.035, -80.869]], "c": true}, "ix": 2}, "nm": "关", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "关", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "PingFang SC"}, {"ch": "联", "size": 12, "style": "Regular", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0.403, -4.028], [0, 0], [0, 0], [0, 0], [11.279, -6.848], [0, 0], [-3.928, 15.509], [-11.581, -11.481], [0, 0], [2.417, 11.078], [0, 0], [0, 0], [0, 0], [0, 5.035], [0, 0], [0, 0], [0, 0], [0, 0], [-3.021, 7.654], [0, 0], [3.827, -6.747]], "o": [[0, 0], [0, 0], [0, 0], [-0.101, 4.431], [0, 0], [0, 0], [0, 0], [-3.122, 15.71], [0, 0], [11.481, -7.452], [2.921, 11.179], [0, 0], [-12.488, -11.481], [0, 0], [0, 0], [0, 0], [0.504, -4.532], [0, 0], [0, 0], [0, 0], [0, 0], [3.223, -5.237], [0, 0], [-2.719, 7.755], [0, 0]], "v": [[46.326, -60.324], [46.326, -53.476], [66.064, -53.476], [66.064, -48.843], [65.259, -36.154], [44.815, -36.154], [44.815, -29.205], [64.252, -29.205], [42.599, 4.633], [46.93, 10.373], [70.093, -24.069], [91.846, 9.97], [96.881, 4.633], [74.524, -29.205], [95.37, -29.205], [95.37, -36.154], [72.208, -36.154], [73.013, -50.455], [73.013, -53.476], [93.658, -53.476], [93.658, -60.324], [78.653, -60.324], [87.918, -79.559], [81.271, -82.077], [71.402, -60.324]], "c": true}, "ix": 2}, "nm": "联", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[4.028, 5.942], [0, 0], [-2.316, -4.834], [0, 0]], "o": [[0, 0], [4.028, 5.841], [0, 0], [-2.216, -4.532]], "v": [[56.296, -81.473], [50.757, -77.948], [60.223, -62.036], [65.662, -65.762]], "c": true}, "ix": 2}, "nm": "联", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-8.057, 2.014], [0, 0], [0, 0], [0, 0], [-2.216, 0.705], [0, 0], [2.316, -0.806], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [8.661, -1.611], [0, 0], [0, 0], [0, 0], [2.216, -0.604], [0, 0], [-2.216, 0.705], [0, 0], [0, 0], [0, 0], [0, 0]], "v": [[4.633, -78.149], [4.633, -71.402], [10.776, -71.402], [10.776, -9.668], [4.431, -8.661], [6.244, -1.813], [31.219, -7.352], [31.219, 10.574], [38.168, 10.574], [38.168, -9.265], [44.916, -11.279], [44.916, -18.329], [38.168, -16.113], [38.168, -71.402], [44.714, -71.402], [44.714, -78.149]], "c": true}, "ix": 2}, "nm": "联", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[4.633, -1.007], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [-4.431, 1.108]], "v": [[17.725, -10.977], [17.725, -29.004], [31.219, -29.004], [31.219, -14.099]], "c": true}, "ix": 2}, "nm": "联", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[17.725, -35.651], [17.725, -50.052], [31.219, -50.052], [31.219, -35.651]], "c": true}, "ix": 2}, "nm": "联", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0]], "v": [[17.725, -56.598], [17.725, -71.402], [31.219, -71.402], [31.219, -56.598]], "c": true}, "ix": 2}, "nm": "联", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "联", "np": 9, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "PingFang SC"}, {"ch": "风", "size": 12, "style": "Regular", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[5.841, 7.251], [0, 0], [-5.438, -7.755], [8.963, -6.848], [0, 0], [-6.244, 9.064], [-4.028, -6.949], [0, 0], [6.143, 8.661], [-3.021, 10.876], [0, 0], [4.028, -7.553]], "o": [[0, 0], [6.546, 8.258], [-6.042, 9.064], [0, 0], [8.661, -6.546], [5.035, 7.452], [0, 0], [-4.33, -7.352], [5.035, -8.862], [0, 0], [-2.417, 8.661], [-4.935, -6.647]], "v": [[33.939, -59.216], [27.997, -55.591], [46.024, -31.622], [23.465, -7.855], [28.299, -1.712], [50.555, -25.076], [64.05, -3.424], [70.596, -7.855], [54.886, -31.924], [66.971, -61.533], [59.821, -62.741], [50.153, -38.37]], "c": true}, "ix": 2}, "nm": "风", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [7.05, -11.783], [0, 0], [-0.201, 19.537], [0, 0], [0, 0], [0, 0], [-2.417, -6.143], [-4.834, 0], [-1.511, 2.417], [-1.208, 7.553], [0, 0], [1.309, 0], [1.108, 4.532], [0, 16.516], [0, 0]], "o": [[0, 0], [-0.302, 17.725], [0, 0], [7.956, -13.293], [0, 0], [0, 0], [0, 0], [0, 18.933], [2.216, 6.244], [1.813, 0], [1.41, -2.82], [0, 0], [-0.906, 9.164], [-1.611, 0], [-1.208, -4.633], [0, 0], [0, 0]], "v": [[16.013, -78.25], [16.013, -39.175], [4.935, 5.136], [10.977, 10.071], [23.264, -39.175], [23.264, -71.201], [73.718, -71.201], [73.718, -37.967], [77.243, -0.403], [87.717, 9.064], [92.752, 5.438], [96.68, -10.071], [90.234, -13.193], [86.911, 0.604], [82.883, -6.244], [81.171, -37.967], [81.171, -78.25]], "c": true}, "ix": 2}, "nm": "风", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "风", "np": 5, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "PingFang SC"}, {"ch": "险", "size": 12, "style": "Regular", "w": 100, "data": {"shapes": [{"ty": "gr", "it": [{"ind": 0, "ty": "sh", "ix": 1, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [4.532, -10.474], [0, -6.949], [1.108, -0.806], [3.424, -0.201], [1.511, 0.201], [0, 0], [-2.82, 2.216], [0, 4.129], [6.244, 8.963], [-3.021, 9.064], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [-2.518, 8.459], [6.747, 8.56], [0, 2.014], [-1.208, 0.806], [-1.007, 0], [0, 0], [6.747, -0.101], [2.014, -1.913], [-0.504, -7.251], [4.028, -9.869], [0, 0], [0, 0]], "v": [[8.56, -77.948], [8.56, 10.172], [15.61, 10.172], [15.61, -71.301], [31.219, -71.301], [20.645, -42.801], [30.817, -19.537], [29.105, -15.308], [22.055, -13.797], [18.228, -14.2], [20.544, -6.949], [34.845, -10.474], [37.866, -19.537], [27.795, -43.909], [38.37, -72.409], [38.37, -77.948]], "c": true}, "ix": 2}, "nm": "险", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 1, "ty": "sh", "ix": 2, "ks": {"a": 0, "k": {"i": [[-3.827, 3.928], [0, 0], [0, 0], [0, 0], [-4.532, -3.223], [0, 0], [5.438, 12.891], [0, 0], [12.085, -8.762], [0, 0]], "o": [[0, 0], [0, 0], [0, 0], [3.525, 3.625], [0, 0], [-13.596, -9.668], [0, 0], [-6.546, 13.898], [0, 0], [4.23, -3.021]], "v": [[50.253, -52.469], [50.253, -46.426], [81.674, -46.426], [81.674, -52.267], [93.759, -42.096], [97.687, -48.138], [69.086, -81.876], [62.54, -81.876], [34.543, -47.836], [38.168, -42.096]], "c": true}, "ix": 2}, "nm": "险", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 2, "ty": "sh", "ix": 3, "ks": {"a": 0, "k": {"i": [[-6.345, -6.546], [0, 0], [-4.431, 8.459]], "o": [[0, 0], [5.539, -6.042], [3.726, 7.855]], "v": [[80.869, -53.073], [50.858, -53.073], [65.762, -74.725]], "c": true}, "ix": 2}, "nm": "险", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 3, "ty": "sh", "ix": 4, "ks": {"a": 0, "k": {"i": [[0, 0], [0, 0], [0, 0], [0, 0], [0, 0], [-3.323, 14.301], [0, 0], [4.733, -10.876]], "o": [[0, 0], [0, 0], [0, 0], [0, 0], [4.733, -11.078], [0, 0], [-3.424, 15.71], [0, 0]], "v": [[35.751, -0.403], [35.751, 6.546], [95.471, 6.546], [95.471, -0.403], [78.351, -0.403], [90.436, -38.37], [83.386, -40.283], [71.1, -0.403]], "c": true}, "ix": 2}, "nm": "险", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 4, "ty": "sh", "ix": 5, "ks": {"a": 0, "k": {"i": [[3.424, 8.459], [0, 0], [-2.417, -9.869], [0, 0]], "o": [[0, 0], [3.726, 9.064], [0, 0], [-2.82, -10.876]], "v": [[47.433, -36.658], [41.19, -34.543], [50.455, -6.042], [56.799, -7.654]], "c": true}, "ix": 2}, "nm": "险", "mn": "ADBE Vector Shape - Group", "hd": false}, {"ind": 5, "ty": "sh", "ix": 6, "ks": {"a": 0, "k": {"i": [[2.921, 8.157], [0, 0], [-2.014, -9.668], [0, 0]], "o": [[0, 0], [3.122, 8.862], [0, 0], [-2.216, -10.574]], "v": [[63.446, -40.585], [57.202, -38.571], [64.957, -10.876], [71.201, -12.488]], "c": true}, "ix": 2}, "nm": "险", "mn": "ADBE Vector Shape - Group", "hd": false}], "nm": "险", "np": 9, "cix": 2, "bm": 0, "ix": 1, "mn": "ADBE Vector Group", "hd": false}]}, "fFamily": "PingFang SC"}]}