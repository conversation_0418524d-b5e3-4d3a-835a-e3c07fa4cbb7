<script setup lang="ts">
import { computed } from 'vue'

const props = defineProps<{
    icon: string // 图标名称，例如 "icon-a-huaban30"
    color?: string // 颜色，可选
    size?: string | number // 图标大小，可选
}>()

const svgStyle = computed(() => ({
    fill: props.color || 'inherit',
}))

const size = computed(() => {
    let pxValue = props.size || 16

    if (typeof props.size === 'string' && props.size.includes('px')) {
        pxValue = props.size.replace('px', '')
    }

    if (isNaN(Number(pxValue))) {
        pxValue = 16
    }

    const remValue = Number(pxValue) / 39
    return `${remValue}rem`
})
</script>

<template>
    <svg class="icon" aria-hidden="true" :style="svgStyle">
        <use :xlink:href="`#${icon}`"></use>
    </svg>
</template>

<style scoped lang="scss">
.icon {
    width: v-bind(size);
    height: v-bind(size);
    font-size: v-bind(size);
    vertical-align: middle;
}
</style>
