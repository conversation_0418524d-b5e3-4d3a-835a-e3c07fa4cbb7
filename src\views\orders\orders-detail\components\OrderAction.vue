<script lang="ts" setup>
import orderService from '@/service/orderService'
import type { IOrderPaymentListItem } from '@/types/order'
import { formatPrice } from '@/utils/format'
import { closeToast, showLoadingToast, showConfirmDialog } from 'vant'
import { computed, inject } from 'vue'
const reload = inject<() => void>('reload', () => {})
const props = defineProps<{
    detail: IOrderPaymentListItem | null
    createOrder: () => void
}>()

const isCanCancel = computed(() => {
    const { order_status } = props.detail || {}
    if (!order_status) return false
    return order_status === '0'
})

const isCanRepay = computed(() => {
    const { order_status } = props.detail || {}
    if (!order_status) return false
    return order_status === '0'
})

const outOrderId = computed(() => {
    const { out_order_id } = props.detail || {}
    if (!out_order_id) return ''
    return out_order_id
})

const actualAmount = computed(() => {
    const { actual_amount } = props.detail || {}
    if (!actual_amount) return ''
    return actual_amount
})

const total_bill_amount = computed(() => {
    const { total_bill_amount } = props.detail || {}
    if (!total_bill_amount) return ''
    return total_bill_amount
})

const cancelOrder = () => {
    showConfirmDialog({
        title: '提示',
        message: '是否取消该订单',
        confirmButtonText: '确认',
        cancelButtonText: '取消',
    }).then(() => {
        showLoadingToast({
            message: '正在取消订单...',
            forbidClick: true,
        })
        orderService
            .goodsOrderCancel({
                orderId: outOrderId.value,
            })
            .then((res) => {
                if (res.errCode === 0) {
                    reload()
                }
            })
            .finally(() => {
                closeToast()
            })
    })
}
</script>

<template>
    <div class="order-action flex flex-1 flex-column flex-end b-padding-44">
        <div class="h-46 flex flex-row top-bottom-center space-between width-100">
            <div class="font-12 lh-16 color-three-grey" v-if="isCanCancel" @click="cancelOrder">取消订单</div>
            <div class="small-primary-btn flex w-278 h-46 font-18" v-if="isCanRepay" @click="createOrder">
                立即支付 ¥{{ actualAmount ? formatPrice(actualAmount) : '' }}
                <span v-if="actualAmount !== total_bill_amount" class="l-margin-6 font-14 font-weight-500 t-padding-4" style="opacity: 0.7; text-decoration: line-through">¥{{ total_bill_amount }}</span>
            </div>
        </div>
    </div>
</template>

<style lang="scss" scoped>
.tips {
    color: #ff854c;
}
</style>
