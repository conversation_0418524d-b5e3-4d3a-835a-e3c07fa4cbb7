<template>
    <div v-for="item in crmList" :key="item.id">
        <div class="flex-center flex-column gap-12" @click="jumpTo(item.path)">
            <Icon :icon="item.icon" :color="item.color" size="36" />
            <div class="font-14">{{ item.label }}</div>
        </div>
    </div>
</template>

<script lang='ts' setup>
import Icon from '@/components/common/Icon.vue'
import { useRouter } from 'vue-router'

const router = useRouter()
const jumpTo = (path: string) => {
    router.push({
        name: path
    })
}
const crmList = [
    {
        id: 1,
        label:'线索池',
        icon:'icon-a-xiansuochi',
        path:'lead-pool',
        value:'lead-pool',
        color:'#3C74EB',
    },
    {
        id: 2,
        label:'线索列表',
        icon:'icon-a-xiansuoliebiao',
        path:'lead-list',
        value:'lead-list',
        color:'#3C74EB',
    },
    {
        id: 3,
        label:'客户公海',
        icon:'icon-a-kehugonghao',
        path:'lead-pool',
        value:'lead-pool',
        color:'#3C74EB',
    },
    {
        id: 4,
        label:'客户列表',
        icon:'icon-a-kehuliebiao',
        path:'lead-list',
        value:'lead-list',
        color:'#3C74EB',
    },
    {
        id: 5,
        label:'风险监控',
        icon:'icon-a-fengxianjiankong',
        path:'lead-list',
        value:'lead-list',
        color:'#3C74EB',
    },
]
</script>

<style lang='scss' scoped>
</style>