{
    "extends": "@vue/tsconfig/tsconfig.dom.json",
    "compilerOptions": {
        "tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo",
        /* Linting */
        "strict": true,
        "noUnusedLocals": true,
        "noUnusedParameters": true,
        "noFallthroughCasesInSwitch": true,
        "noUncheckedSideEffectImports": true,
        "types": [
            "vite/client",
            "node"
        ],
        "baseUrl": ".",
        "paths": {
            "@/*": [
                "src/*"
            ]
        }
        // "noImplicitAny": false,
        // "allowJs": true,
    },
    "include": [
        "src/**/*.ts",
        "src/**/*.tsx",
        "src/**/*.vue",
        "src/**/*.ts"
    ]
}