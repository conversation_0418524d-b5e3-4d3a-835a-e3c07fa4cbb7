<script lang="ts" setup>
import CountDownTimer from '@/components/count-down-timer/CountDownTimer.vue'
import type { IOrderPaymentListItem } from '@/types/order'
import { computed, inject } from 'vue'
const reload = inject<() => void>('reload', () => {})
const props = defineProps<{
    detail: IOrderPaymentListItem | null
}>()

const statusIconList = [
    {
        value: '1',
        icon: 'passed',
    },
    {
        value: '0',
        icon: 'clock-o',
    },
    {
        value: '2',
        icon: 'close',
    },
    {
        value: '4',
        icon: 'close',
    },
    {
        value: '3',
        icon: 'close',
    },
]

const isPending = computed(() => {
    const { order_status } = props.detail || {}
    return order_status === '0'
})

const isShowTimer = computed(() => {
    const now = new Date()
    const diff = endTime.value.getTime() - now.getTime()

    return diff > 0
})

const startTime = computed(() => {
    const { created_at } = props.detail || {}
    if (!created_at) return ''
    return created_at
})

const durationTime = computed(() => {
    const { delay_time } = props.detail || {}
    if (!delay_time || isNaN(Number(delay_time))) return 0
    return Number(delay_time)
})

const endTime = computed<Date>(() => {
    const start = new Date(startTime.value)
    return new Date(start.getTime() + durationTime.value * 60 * 1000)
})

const statusStr = computed(() => {
    const { order_status_str } = props.detail || {}
    if (!order_status_str) return ''
    return order_status_str
})

const statusIcon = () => {
    const { order_status } = props.detail || {}
    const target = statusIconList.find((e) => e.value === order_status)
    if (!target) return
    return target.icon
}
</script>

<template>
    <div class="order-detail flex flex-column center gap-8">
        <div class="flex flex-row center gap-6">
            <div class="oh flex-center">
                <van-icon :name="statusIcon()" class="status-icon" />
            </div>
            <div class="font-16 color-black font-weight-500 lh-22">{{ statusStr }}</div>
        </div>
        <div class="font-14 lh-20 color-black flex flex-row" v-if="isShowTimer && isPending">
            剩余
            <div class="color-blue lr-padding-6 font-weight-500">
                <CountDownTimer
                    :start-time="startTime"
                    :duration-minutes="durationTime"
                    display-format="auto"
                    :time-ended="reload"
                />
            </div>
            订单自动取消
        </div>
    </div>
</template>

<style lang="scss" scoped>
.status-icon {
    font-size: 18px;
}
</style>
